#!/bin/bash

# Generate Swagger documentation for GoKeys API
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}GoKeys API Documentation Generator${NC}"
echo "---"

# Check if swag is installed
SWAG_CMD=""
if command -v swag &> /dev/null; then
    SWAG_CMD="swag"
elif [ -f "$HOME/go/bin/swag" ]; then
    SWAG_CMD="$HOME/go/bin/swag"
else
    echo -e "${YELLOW}Installing swag tool...${NC}"
    go install github.com/swaggo/swag/cmd/swag@latest

    if [ -f "$HOME/go/bin/swag" ]; then
        SWAG_CMD="$HOME/go/bin/swag"
        echo -e "${GREEN}✓ Swag installed successfully${NC}"
    else
        echo -e "${RED}Failed to install swag. Please install manually:${NC}"
        echo "go install github.com/swaggo/swag/cmd/swag@latest"
        echo "Make sure \$HOME/go/bin is in your PATH or run: export PATH=\$PATH:\$HOME/go/bin"
        exit 1
    fi
fi

# Clean previous documentation
echo -e "${YELLOW}Cleaning previous documentation...${NC}"
rm -rf docs/docs.go docs/swagger.yaml docs/swagger.json

# Generate Swagger documentation
echo -e "${YELLOW}Generating Swagger documentation...${NC}"
$SWAG_CMD init \
    -g cmd/server/main.go \
    -o docs \
    --parseDependency \
    --parseInternal \
    --parseDepth 1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Documentation generated successfully!${NC}"
    echo ""
    echo "Generated files:"
    echo "  - docs/docs.go"
    echo "  - docs/swagger.yaml" 
    echo "  - docs/swagger.json"
    echo ""
    echo "Access documentation at:"
    echo "  - http://localhost:8080/swagger/index.html (when server is running)"
    echo "  - http://localhost:8080/docs (redirect to Swagger UI)"
else
    echo -e "${RED}✗ Documentation generation failed!${NC}"
    exit 1
fi

# Validate generated documentation
echo -e "${YELLOW}Validating generated documentation...${NC}"

# Check if required files exist
if [ -f "docs/docs.go" ] && [ -f "docs/swagger.yaml" ] && [ -f "docs/swagger.json" ]; then
    echo -e "${GREEN}✓ All documentation files generated${NC}"
else
    echo -e "${RED}✗ Some documentation files are missing${NC}"
    exit 1
fi

# Check if docs.go contains valid Go code
if go fmt docs/docs.go > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Generated Go code is valid${NC}"
else
    echo -e "${RED}✗ Generated Go code has syntax errors${NC}"
    exit 1
fi

# Check if JSON is valid
if python3 -m json.tool docs/swagger.json > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Generated JSON is valid${NC}"
elif command -v jq &> /dev/null && jq empty docs/swagger.json > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Generated JSON is valid${NC}"
else
    echo -e "${YELLOW}⚠ Could not validate JSON (python3 or jq not available)${NC}"
fi

# Check if YAML is valid
if python3 -c "import yaml; yaml.safe_load(open('docs/swagger.yaml'))" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Generated YAML is valid${NC}"
elif command -v yq &> /dev/null && yq eval docs/swagger.yaml > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Generated YAML is valid${NC}"
else
    echo -e "${YELLOW}⚠ Could not validate YAML (python3 yaml or yq not available)${NC}"
fi

echo ""
echo -e "${GREEN}Documentation generation completed successfully!${NC}"
echo ""
echo "Usage:"
echo "  1. Start the GoKeys server: go run cmd/server/main.go"
echo "  2. Open http://localhost:8080/swagger/index.html in your browser"
echo "  3. Explore and test the API endpoints"
echo ""
echo "Notes:"
echo "  - Swagger UI is only available in development and staging environments"
echo "  - In production, API documentation is served as static files only"
echo "  - Update API annotations in handlers when adding new endpoints"