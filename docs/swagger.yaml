basePath: /api/v1
definitions:
  handlers.AccountCreateRequest:
    description: Request payload for creating a new account
    properties:
      email:
        example: <EMAIL>
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Acme Corporation
        maxLength: 255
        minLength: 1
        type: string
      slug:
        example: acme-corp
        maxLength: 255
        minLength: 1
        type: string
    required:
    - email
    - name
    - slug
    type: object
  handlers.AccountListResponse:
    description: Paginated list of accounts
    properties:
      accounts:
        items:
          $ref: '#/definitions/handlers.AccountResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.AccountResponse:
    description: Account information returned by the API
    properties:
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Acme Corporation
        type: string
      slug:
        example: acme-corp
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
    type: object
  handlers.AccountUpdateRequest:
    description: Request payload for updating an account
    properties:
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Acme Corporation
        maxLength: 255
        minLength: 1
        type: string
      slug:
        example: updated-acme
        maxLength: 255
        minLength: 1
        type: string
    type: object
  handlers.LicenseCreateRequest:
    description: Request payload for creating a new license
    properties:
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Enterprise License
        maxLength: 255
        minLength: 1
        type: string
      policy_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      user_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
    required:
    - name
    - policy_id
    type: object
  handlers.LicenseInfoResponse:
    description: License information returned by info endpoints
    properties:
      account_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      claims:
        additionalProperties: true
        type: object
      expired:
        example: false
        type: boolean
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      format:
        example: json
        type: string
      product_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      status:
        example: ACTIVE
        type: string
      suspended:
        example: false
        type: boolean
    type: object
  handlers.LicenseListResponse:
    description: Paginated list of licenses
    properties:
      licenses:
        items:
          $ref: '#/definitions/handlers.LicenseResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.LicenseResponse:
    description: License information returned by the API
    properties:
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Enterprise License
        type: string
      policy_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      status:
        example: ACTIVE
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
      user_id:
        example: 550e8400-e29b-41d4-a716-446655440002
        type: string
    type: object
  handlers.LicenseUpdateRequest:
    description: Request payload for updating a license
    properties:
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Enterprise License
        maxLength: 255
        minLength: 1
        type: string
      status:
        enum:
        - ACTIVE
        - SUSPENDED
        - EXPIRED
        example: ACTIVE
        type: string
    type: object
  handlers.PaginationInfo:
    description: Pagination information for list responses
    properties:
      page:
        example: 1
        type: integer
      per_page:
        example: 20
        type: integer
      total:
        example: 150
        type: integer
      total_pages:
        example: 8
        type: integer
    type: object
  handlers.ValidateLicenseRequest:
    description: Request payload for validating a license
    properties:
      environment:
        example: production
        type: string
      license_key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      machine_fingerprint:
        example: fp-mac-********
        type: string
      machine_info:
        additionalProperties: true
        type: object
    required:
    - license_key
    type: object
  handlers.ValidateLicenseResponse:
    description: Response payload for license validation
    properties:
      account: {}
      cache_hit:
        example: false
        type: boolean
      claims:
        additionalProperties: true
        type: object
      errors:
        items:
          type: string
        type: array
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      license: {}
      machines_allowed:
        example: 5
        type: integer
      machines_used:
        example: 2
        type: integer
      policy: {}
      valid:
        example: true
        type: boolean
      validation_time:
        example: "2025-07-12T16:15:30Z"
        type: string
      warnings:
        items:
          type: string
        type: array
    type: object
  responses.ErrorCode:
    enum:
    - UNAUTHORIZED
    - INVALID_TOKEN
    - TOKEN_EXPIRED
    - INSUFFICIENT_SCOPE
    - FORBIDDEN
    - INSUFFICIENT_PERMISSIONS
    - VALIDATION_FAILED
    - INVALID_REQUEST
    - MISSING_PARAMETER
    - INVALID_PARAMETER
    - NOT_FOUND
    - RESOURCE_NOT_FOUND
    - CONFLICT
    - RESOURCE_EXISTS
    - LICENSE_EXPIRED
    - LICENSE_SUSPENDED
    - LICENSE_INVALID
    - LICENSE_NOT_FOUND
    - MACHINE_HEARTBEAT_DEAD
    - TOO_MANY_MACHINES
    - NO_MACHINE
    - CHECKOUT_ALGORITHM_INVALID
    - CHECKOUT_INCLUDE_INVALID
    - CHECKOUT_TTL_INVALID
    - INTERNAL_ERROR
    - SERVICE_UNAVAILABLE
    - RATE_LIMIT_EXCEEDED
    type: string
    x-enum-varnames:
    - ErrorCodeUnauthorized
    - ErrorCodeInvalidToken
    - ErrorCodeTokenExpired
    - ErrorCodeInsufficientScope
    - ErrorCodeForbidden
    - ErrorCodeInsufficientPermissions
    - ErrorCodeValidationFailed
    - ErrorCodeInvalidRequest
    - ErrorCodeMissingParameter
    - ErrorCodeInvalidParameter
    - ErrorCodeNotFound
    - ErrorCodeResourceNotFound
    - ErrorCodeConflict
    - ErrorCodeResourceExists
    - ErrorCodeLicenseExpired
    - ErrorCodeLicenseSuspended
    - ErrorCodeLicenseInvalid
    - ErrorCodeLicenseNotFound
    - ErrorCodeMachineHeartbeatDead
    - ErrorCodeTooManyMachines
    - ErrorCodeNoMachine
    - ErrorCodeCheckoutAlgorithmInvalid
    - ErrorCodeCheckoutIncludeInvalid
    - ErrorCodeCheckoutTTLInvalid
    - ErrorCodeInternalError
    - ErrorCodeServiceUnavailable
    - ErrorCodeRateLimitExceeded
  responses.ErrorDetail:
    properties:
      code:
        $ref: '#/definitions/responses.ErrorCode'
      detail:
        type: string
      meta:
        additionalProperties: true
        type: object
      source:
        additionalProperties: true
        type: object
      title:
        type: string
    type: object
  responses.ErrorResponse:
    properties:
      errors:
        items:
          $ref: '#/definitions/responses.ErrorDetail'
        type: array
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: GoKeys API Support
    url: https://gokeys.com/support
  description: Enterprise License Management Platform with comprehensive license validation,
    machine tracking, and policy management capabilities.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://gokeys.com/terms
  title: GoKeys License Management API
  version: "1.0"
paths:
  /accounts:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all accounts (admin only)
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of accounts retrieved successfully
          schema:
            $ref: '#/definitions/handlers.AccountListResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List accounts
      tags:
      - Accounts
    post:
      consumes:
      - application/json
      description: Create a new account for license management
      parameters:
      - description: Account creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AccountCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Account created successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new account
      tags:
      - Accounts
  /accounts/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific account
      parameters:
      - description: Account ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Account retrieved successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid account ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get account by ID
      tags:
      - Accounts
    put:
      consumes:
      - application/json
      description: Update an existing account's properties
      parameters:
      - description: Account ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AccountUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Account updated successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid request data or account ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update account
      tags:
      - Accounts
  /licenses:
    get:
      consumes:
      - application/json
      description: Get a paginated list of licenses for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 20, max: 100)'
        example: 20
        in: query
        name: page_size
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of licenses retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List licenses
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Create a new license for a product with specified policy
      parameters:
      - description: License creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.LicenseCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: License created successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new license
      tags:
      - Licenses
  /licenses/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a license by ID (soft delete)
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: License deleted successfully
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete license
      tags:
      - Licenses
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific license
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid license ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get license by ID
      tags:
      - Licenses
    put:
      consumes:
      - application/json
      description: Update an existing license's properties
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: License update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.LicenseUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: License updated successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid request data or license ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update license
      tags:
      - Licenses
  /licenses/info:
    get:
      consumes:
      - application/json
      description: Retrieves detailed information about a license without performing
        validation
      parameters:
      - description: License key to get info for
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License information retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseInfoResponse'
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get license information
      tags:
      - Licenses
  /licenses/quick-validate:
    get:
      consumes:
      - application/json
      description: Performs a quick validation of a license key without detailed tracking
      parameters:
      - description: License key to validate
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License is valid
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "403":
          description: License is invalid
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Quick license validation
      tags:
      - Licenses
  /licenses/validate:
    post:
      consumes:
      - application/json
      description: Validates a license key and returns detailed validation result
        including machine tracking
      parameters:
      - description: License validation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ValidateLicenseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: License validation successful
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Validate license
      tags:
      - Licenses
securityDefinitions:
  ApiKeyAuth:
    description: API key for authentication
    in: header
    name: X-API-Key
    type: apiKey
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
  LicenseKeyAuth:
    description: License key for validation endpoints
    in: header
    name: X-License-Key
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization endpoints
  name: Authentication
- description: License management and validation
  name: Licenses
- description: Account management
  name: Accounts
- description: Product management
  name: Products
- description: Policy configuration
  name: Policies
- description: Machine registration and tracking
  name: Machines
- description: User management
  name: Users
- description: System health and monitoring
  name: Health
- description: System metrics and analytics
  name: Metrics
