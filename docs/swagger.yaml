basePath: /api/v1
definitions:
  handlers.AccountCreateRequest:
    description: Request payload for creating a new account
    properties:
      email:
        example: <EMAIL>
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Acme Corporation
        maxLength: 255
        minLength: 1
        type: string
      slug:
        example: acme-corp
        maxLength: 255
        minLength: 1
        type: string
    required:
    - email
    - name
    - slug
    type: object
  handlers.AccountListResponse:
    description: Paginated list of accounts
    properties:
      accounts:
        items:
          $ref: '#/definitions/handlers.AccountResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.AccountResponse:
    description: Account information returned by the API
    properties:
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Acme Corporation
        type: string
      slug:
        example: acme-corp
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
    type: object
  handlers.AccountUpdateRequest:
    description: Request payload for updating an account
    properties:
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Acme Corporation
        maxLength: 255
        minLength: 1
        type: string
      slug:
        example: updated-acme
        maxLength: 255
        minLength: 1
        type: string
    type: object
  handlers.EntitlementCreateRequest:
    description: Request payload for creating a new entitlement
    properties:
      code:
        example: premium-features
        maxLength: 255
        minLength: 1
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Premium Features
        maxLength: 255
        minLength: 1
        type: string
    required:
    - code
    - name
    type: object
  handlers.EntitlementListResponse:
    description: Paginated list of entitlements
    properties:
      entitlements:
        items:
          $ref: '#/definitions/handlers.EntitlementResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.EntitlementResponse:
    description: Entitlement information returned by the API
    properties:
      code:
        example: premium-features
        type: string
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Premium Features
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
    type: object
  handlers.EntitlementUpdateRequest:
    description: Request payload for updating an entitlement
    properties:
      code:
        example: updated-premium
        maxLength: 255
        minLength: 1
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Premium Features
        maxLength: 255
        minLength: 1
        type: string
    type: object
  handlers.GroupCreateRequest:
    description: Request payload for creating a new group
    properties:
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      max_licenses:
        example: 5
        minimum: 0
        type: integer
      max_machines:
        example: 20
        minimum: 0
        type: integer
      max_users:
        example: 10
        minimum: 0
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Development Team
        maxLength: 255
        minLength: 1
        type: string
    required:
    - name
    type: object
  handlers.GroupListResponse:
    description: Paginated list of groups
    properties:
      groups:
        items:
          $ref: '#/definitions/handlers.GroupResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.GroupResponse:
    description: Group information returned by the API
    properties:
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      max_licenses:
        example: 5
        type: integer
      max_machines:
        example: 20
        type: integer
      max_users:
        example: 10
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Development Team
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
    type: object
  handlers.GroupUpdateRequest:
    description: Request payload for updating a group
    properties:
      max_licenses:
        example: 8
        minimum: 0
        type: integer
      max_machines:
        example: 30
        minimum: 0
        type: integer
      max_users:
        example: 15
        minimum: 0
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Development Team
        maxLength: 255
        minLength: 1
        type: string
    type: object
  handlers.LicenseCreateRequest:
    description: Request payload for creating a new license
    properties:
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Enterprise License
        maxLength: 255
        minLength: 1
        type: string
      policy_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      user_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
    required:
    - name
    - policy_id
    type: object
  handlers.LicenseInfoResponse:
    description: License information returned by info endpoints
    properties:
      account_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      claims:
        additionalProperties: true
        type: object
      expired:
        example: false
        type: boolean
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      format:
        example: json
        type: string
      product_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      status:
        example: ACTIVE
        type: string
      suspended:
        example: false
        type: boolean
    type: object
  handlers.LicenseListResponse:
    description: Paginated list of licenses
    properties:
      licenses:
        items:
          $ref: '#/definitions/handlers.LicenseResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.LicenseResponse:
    description: License information returned by the API
    properties:
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Enterprise License
        type: string
      policy_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      status:
        example: ACTIVE
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
      user_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
    type: object
  handlers.LicenseUpdateRequest:
    description: Request payload for updating a license
    properties:
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Enterprise License
        maxLength: 255
        minLength: 1
        type: string
      status:
        enum:
        - ACTIVE
        - SUSPENDED
        - EXPIRED
        example: ACTIVE
        type: string
    type: object
  handlers.MachineCreateRequest:
    description: Request payload for creating a new machine
    properties:
      cores:
        example: 8
        minimum: 1
        type: integer
      fingerprint:
        example: fp-mac-********
        maxLength: 255
        minLength: 1
        type: string
      hostname:
        example: dev-machine-01
        type: string
      ip:
        example: *************
        type: string
      license_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Development Machine
        type: string
      platform:
        example: windows
        type: string
    required:
    - fingerprint
    - license_id
    type: object
  handlers.MachineListResponse:
    description: Paginated list of machines
    properties:
      machines:
        items:
          $ref: '#/definitions/handlers.MachineResponse'
        type: array
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
    type: object
  handlers.MachineResponse:
    description: Machine information returned by the API
    properties:
      cores:
        example: 8
        type: integer
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-*********005
        type: string
      fingerprint:
        example: fp-mac-********
        type: string
      group_id:
        example: 550e8400-e29b-41d4-a716-*********003
        type: string
      hostname:
        example: dev-machine-01
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      ip:
        example: *************
        type: string
      last_seen:
        example: "2025-07-15T14:30:00Z"
        type: string
      license_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Development Machine
        type: string
      owner_id:
        example: 550e8400-e29b-41d4-a716-*********004
        type: string
      platform:
        example: windows
        type: string
      policy_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      status:
        example: ACTIVE
        type: string
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
    type: object
  handlers.MachineUpdateRequest:
    description: Request payload for updating a machine
    properties:
      cores:
        example: 16
        minimum: 1
        type: integer
      fingerprint:
        example: fp-mac-87654321
        maxLength: 255
        minLength: 1
        type: string
      hostname:
        example: updated-dev-machine
        type: string
      ip:
        example: *************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Development Machine
        type: string
      platform:
        example: linux
        type: string
    type: object
  handlers.PaginationInfo:
    description: Pagination information for list responses
    properties:
      page:
        example: 1
        type: integer
      per_page:
        example: 20
        type: integer
      total:
        example: 150
        type: integer
      total_pages:
        example: 8
        type: integer
    type: object
  handlers.PolicyCreateRequest:
    description: Request payload for creating a new policy
    properties:
      check_in_interval:
        example: daily
        type: string
      check_in_interval_count:
        example: 1
        minimum: 0
        type: integer
      description:
        example: A standard policy for regular licenses
        type: string
      duration:
        example: 365
        minimum: 0
        type: integer
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      expiration_strategy:
        enum:
        - RESTRICT_ACCESS
        - REVOKE_ACCESS
        - MAINTAIN_ACCESS
        example: RESTRICT_ACCESS
        type: string
      floating:
        example: false
        type: boolean
      heartbeat_duration:
        example: 3600
        minimum: 0
        type: integer
      machine_uniqueness_strategy:
        enum:
        - UNIQUE_PER_ACCOUNT
        - UNIQUE_PER_PRODUCT
        - UNIQUE_PER_POLICY
        - UNIQUE_PER_LICENSE
        example: UNIQUE_PER_LICENSE
        type: string
      max_activations:
        example: 10
        minimum: 0
        type: integer
      max_cores:
        example: 8
        minimum: 0
        type: integer
      max_deactivations:
        example: 5
        minimum: 0
        type: integer
      max_machines:
        description: Machine limits
        example: 5
        minimum: 0
        type: integer
      max_processes:
        example: 10
        minimum: 0
        type: integer
      max_users:
        example: 3
        minimum: 0
        type: integer
      max_uses:
        example: 100
        minimum: 0
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Standard License Policy
        maxLength: 255
        minLength: 1
        type: string
      overage_strategy:
        enum:
        - NO_OVERAGE
        - ALWAYS_ALLOW_OVERAGE
        - ALLOW_1_25X_OVERAGE
        - ALLOW_1_5X_OVERAGE
        - ALLOW_2X_OVERAGE
        example: NO_OVERAGE
        type: string
      product_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      protected:
        example: false
        type: boolean
      require_check_in:
        description: Check-in settings
        example: false
        type: boolean
      require_heartbeat:
        example: true
        type: boolean
      scheme:
        description: Advanced settings (simplified)
        enum:
        - ED25519_SIGN
        - RSA_PKCS1_SIGN
        - RSA_PSS_SIGN
        example: ED25519_SIGN
        type: string
      strict:
        example: true
        type: boolean
      use_pool:
        description: Pool and activation limits
        example: false
        type: boolean
    required:
    - name
    - product_id
    type: object
  handlers.PolicyListResponse:
    description: Paginated list of policies
    properties:
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
      policies:
        items:
          $ref: '#/definitions/handlers.PolicyResponse'
        type: array
    type: object
  handlers.PolicyResponse:
    description: Policy information returned by the API
    properties:
      check_in_interval:
        example: daily
        type: string
      check_in_interval_count:
        example: 1
        type: integer
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      description:
        example: A standard policy for regular licenses
        type: string
      duration:
        example: 365
        type: integer
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      expiration_strategy:
        example: RESTRICT_ACCESS
        type: string
      floating:
        example: false
        type: boolean
      heartbeat_duration:
        example: 3600
        type: integer
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      machine_uniqueness_strategy:
        example: UNIQUE_PER_LICENSE
        type: string
      max_activations:
        example: 10
        type: integer
      max_cores:
        example: 8
        type: integer
      max_deactivations:
        example: 5
        type: integer
      max_machines:
        example: 5
        type: integer
      max_processes:
        example: 10
        type: integer
      max_users:
        example: 3
        type: integer
      max_uses:
        example: 100
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Standard License Policy
        type: string
      overage_strategy:
        example: NO_OVERAGE
        type: string
      product_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      protected:
        example: false
        type: boolean
      require_check_in:
        example: false
        type: boolean
      require_heartbeat:
        example: true
        type: boolean
      scheme:
        example: ED25519_SIGN
        type: string
      strict:
        example: true
        type: boolean
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
      use_pool:
        example: false
        type: boolean
    type: object
  handlers.PolicyUpdateRequest:
    description: Request payload for updating a policy
    properties:
      check_in_interval:
        example: weekly
        type: string
      check_in_interval_count:
        example: 2
        minimum: 0
        type: integer
      description:
        example: An updated standard policy for regular licenses
        type: string
      duration:
        example: 730
        minimum: 0
        type: integer
      expiration_strategy:
        example: REVOKE_ACCESS
        type: string
      floating:
        example: true
        type: boolean
      heartbeat_duration:
        example: 7200
        minimum: 0
        type: integer
      machine_uniqueness_strategy:
        example: UNIQUE_PER_POLICY
        type: string
      max_activations:
        example: 20
        minimum: 0
        type: integer
      max_cores:
        example: 16
        minimum: 0
        type: integer
      max_deactivations:
        example: 10
        minimum: 0
        type: integer
      max_machines:
        description: Machine limits
        example: 10
        minimum: 0
        type: integer
      max_processes:
        example: 20
        minimum: 0
        type: integer
      max_users:
        example: 5
        minimum: 0
        type: integer
      max_uses:
        example: 200
        minimum: 0
        type: integer
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Standard License Policy
        maxLength: 255
        minLength: 1
        type: string
      overage_strategy:
        example: ALLOW_1_25X_OVERAGE
        type: string
      protected:
        example: true
        type: boolean
      require_check_in:
        description: Check-in settings
        example: true
        type: boolean
      require_heartbeat:
        example: false
        type: boolean
      scheme:
        description: Advanced settings
        enum:
        - ED25519_SIGN
        - RSA_PKCS1_SIGN
        - RSA_PSS_SIGN
        example: RSA_PKCS1_SIGN
        type: string
      strict:
        example: false
        type: boolean
      use_pool:
        description: Pool and activation limits
        example: true
        type: boolean
    type: object
  handlers.ProductCreateRequest:
    description: Request payload for creating a new product
    properties:
      code:
        example: my-software
        maxLength: 255
        minLength: 1
        type: string
      description:
        example: A comprehensive software solution
        type: string
      distribution_strategy:
        enum:
        - licensed
        - open
        example: licensed
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: My Software Product
        maxLength: 255
        minLength: 1
        type: string
      platforms:
        example:
        - windows
        - macos
        - linux
        items:
          type: string
        type: array
      url:
        example: https://example.com/product
        type: string
    required:
    - code
    - name
    type: object
  handlers.ProductListResponse:
    description: Paginated list of products
    properties:
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
      products:
        items:
          $ref: '#/definitions/handlers.ProductResponse'
        type: array
    type: object
  handlers.ProductResponse:
    description: Product information returned by the API
    properties:
      code:
        example: my-software
        type: string
      created_at:
        example: "2025-01-01T00:00:00Z"
        type: string
      description:
        example: A comprehensive software solution
        type: string
      distribution_strategy:
        example: licensed
        type: string
      environment_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: My Software Product
        type: string
      platforms:
        example:
        - windows
        - macos
        - linux
        items:
          type: string
        type: array
      updated_at:
        example: "2025-01-15T10:30:00Z"
        type: string
      url:
        example: https://example.com/product
        type: string
    type: object
  handlers.ProductUpdateRequest:
    description: Request payload for updating a product
    properties:
      code:
        example: updated-software
        maxLength: 255
        minLength: 1
        type: string
      description:
        example: An updated comprehensive software solution
        type: string
      distribution_strategy:
        enum:
        - licensed
        - open
        example: licensed
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        example: Updated Software Product
        maxLength: 255
        minLength: 1
        type: string
      platforms:
        example:
        - windows
        - macos
        - linux
        - android
        items:
          type: string
        type: array
      url:
        example: https://example.com/updated-product
        type: string
    type: object
  handlers.ValidateLicenseRequest:
    description: Request payload for validating a license
    properties:
      environment:
        example: production
        type: string
      license_key:
        example: LIC-12345-ABCDE-67890-FGHIJ
        type: string
      machine_fingerprint:
        example: fp-mac-********
        type: string
      machine_info:
        additionalProperties: true
        type: object
    required:
    - license_key
    type: object
  handlers.ValidateLicenseResponse:
    description: Response payload for license validation
    properties:
      account: {}
      cache_hit:
        example: false
        type: boolean
      claims:
        additionalProperties: true
        type: object
      errors:
        items:
          type: string
        type: array
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      license: {}
      machines_allowed:
        example: 5
        type: integer
      machines_used:
        example: 2
        type: integer
      policy: {}
      valid:
        example: true
        type: boolean
      validation_time:
        example: "2025-07-12T16:15:30Z"
        type: string
      warnings:
        items:
          type: string
        type: array
    type: object
  handlers.WebhookEndpointRequest:
    description: Request payload for webhook endpoint operations
    properties:
      data:
        properties:
          attributes:
            properties:
              api_version:
                example: v1
                type: string
              description:
                example: Webhook for production environment
                type: string
              enabled:
                example: true
                type: boolean
              events:
                example:
                - license.created
                - license.updated
                items:
                  type: string
                type: array
              max_retries:
                example: 3
                type: integer
              name:
                example: Production Webhook
                type: string
              retry_delay:
                example: 5
                type: integer
              signature_algorithm:
                example: sha256
                type: string
              url:
                example: https://api.example.com/webhooks
                type: string
            required:
            - url
            type: object
          relationships:
            properties:
              environment:
                properties:
                  data:
                    properties:
                      id:
                        example: 550e8400-e29b-41d4-a716-************
                        type: string
                      type:
                        example: environments
                        type: string
                    type: object
                type: object
            type: object
          type:
            example: webhook-endpoints
            type: string
        required:
        - type
        type: object
    type: object
  responses.ErrorCode:
    enum:
    - UNAUTHORIZED
    - INVALID_TOKEN
    - TOKEN_EXPIRED
    - INSUFFICIENT_SCOPE
    - FORBIDDEN
    - INSUFFICIENT_PERMISSIONS
    - VALIDATION_FAILED
    - INVALID_REQUEST
    - MISSING_PARAMETER
    - INVALID_PARAMETER
    - NOT_FOUND
    - RESOURCE_NOT_FOUND
    - CONFLICT
    - RESOURCE_EXISTS
    - LICENSE_EXPIRED
    - LICENSE_SUSPENDED
    - LICENSE_INVALID
    - LICENSE_NOT_FOUND
    - MACHINE_HEARTBEAT_DEAD
    - TOO_MANY_MACHINES
    - NO_MACHINE
    - CHECKOUT_ALGORITHM_INVALID
    - CHECKOUT_INCLUDE_INVALID
    - CHECKOUT_TTL_INVALID
    - INTERNAL_ERROR
    - SERVICE_UNAVAILABLE
    - RATE_LIMIT_EXCEEDED
    type: string
    x-enum-varnames:
    - ErrorCodeUnauthorized
    - ErrorCodeInvalidToken
    - ErrorCodeTokenExpired
    - ErrorCodeInsufficientScope
    - ErrorCodeForbidden
    - ErrorCodeInsufficientPermissions
    - ErrorCodeValidationFailed
    - ErrorCodeInvalidRequest
    - ErrorCodeMissingParameter
    - ErrorCodeInvalidParameter
    - ErrorCodeNotFound
    - ErrorCodeResourceNotFound
    - ErrorCodeConflict
    - ErrorCodeResourceExists
    - ErrorCodeLicenseExpired
    - ErrorCodeLicenseSuspended
    - ErrorCodeLicenseInvalid
    - ErrorCodeLicenseNotFound
    - ErrorCodeMachineHeartbeatDead
    - ErrorCodeTooManyMachines
    - ErrorCodeNoMachine
    - ErrorCodeCheckoutAlgorithmInvalid
    - ErrorCodeCheckoutIncludeInvalid
    - ErrorCodeCheckoutTTLInvalid
    - ErrorCodeInternalError
    - ErrorCodeServiceUnavailable
    - ErrorCodeRateLimitExceeded
  responses.ErrorDetail:
    properties:
      code:
        $ref: '#/definitions/responses.ErrorCode'
      detail:
        type: string
      meta:
        additionalProperties: true
        type: object
      source:
        additionalProperties: true
        type: object
      title:
        type: string
    type: object
  responses.ErrorResponse:
    properties:
      errors:
        items:
          $ref: '#/definitions/responses.ErrorDetail'
        type: array
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: GoKeys API Support
    url: https://gokeys.com/support
  description: Enterprise License Management Platform with comprehensive license validation,
    machine tracking, and policy management capabilities.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://gokeys.com/terms
  title: GoKeys License Management API
  version: "1.0"
paths:
  /account:
    get:
      consumes:
      - application/json
      description: Retrieve information about the currently authenticated account
      produces:
      - application/json
      responses:
        "200":
          description: Current account retrieved successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get current account
      tags:
      - Accounts
    put:
      consumes:
      - application/json
      description: Update the currently authenticated account's properties
      parameters:
      - description: Account update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AccountUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Current account updated successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update current account
      tags:
      - Accounts
  /accounts:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all accounts (admin only)
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of accounts retrieved successfully
          schema:
            $ref: '#/definitions/handlers.AccountListResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List accounts
      tags:
      - Accounts
    post:
      consumes:
      - application/json
      description: Create a new account for license management
      parameters:
      - description: Account creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AccountCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Account created successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new account
      tags:
      - Accounts
  /accounts/{account_id}/licenses:
    get:
      consumes:
      - application/json
      description: Get a paginated list of licenses for the authenticated account
      parameters:
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 20, max: 100)'
        example: 20
        in: query
        name: page_size
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of licenses retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List licenses
      tags:
      - Licenses
  /accounts/{account_id}/licenses/{id}/actions/check-out:
    get:
      consumes:
      - application/json
      description: Generate and retrieve a signed license certificate for offline
        validation
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: License certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout license certificate
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Generate and retrieve a signed license certificate for offline
        validation
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: License certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout license certificate
      tags:
      - Licenses
  /accounts/{account_id}/licenses/{id}/actions/validate:
    get:
      consumes:
      - application/json
      description: Performs a quick validation of a license by its ID or license key
        without detailed tracking
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Quick validate license by ID or key
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Validates a license by its ID or license key with optional machine
        tracking
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Machine fingerprint for validation
        example: '"fp-mac-********"'
        in: query
        name: machine_fingerprint
        type: string
      - description: Environment for validation
        example: '"production"'
        in: query
        name: environment
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation successful
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: License validation failed
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Validate license by ID or key
      tags:
      - Licenses
  /accounts/{account_id}/licenses/actions/validate-key:
    post:
      consumes:
      - application/json
      description: Validates a license using the license key provided in request body
      parameters:
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Request with license key in meta.key field
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: License validation result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request format
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Validate license by key
      tags:
      - Licenses
  /accounts/{account_id}/machines/{id}/actions/check-out:
    get:
      consumes:
      - application/json
      description: Generate and retrieve a signed machine certificate for offline
        validation
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: Machine certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout machine certificate
      tags:
      - Machines
    post:
      consumes:
      - application/json
      description: Generate and retrieve a signed machine certificate for offline
        validation
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: Machine certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout machine certificate
      tags:
      - Machines
  /accounts/{account_id}/machines/{id}/actions/ping:
    post:
      consumes:
      - application/json
      description: Send a heartbeat ping for a machine to indicate it's still active
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Heartbeat ping successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid machine ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Machine not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Send machine heartbeat ping
      tags:
      - Machines
  /accounts/{account_id}/machines/{id}/actions/reset:
    post:
      consumes:
      - application/json
      description: Reset the heartbeat status for a machine
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account ID (for account-scoped routes)
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: account_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Heartbeat reset successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid machine ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Machine not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Reset machine heartbeat
      tags:
      - Machines
  /accounts/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an account by ID (soft delete)
      parameters:
      - description: Account ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Account deleted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid account ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete account
      tags:
      - Accounts
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific account
      parameters:
      - description: Account ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Account retrieved successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid account ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get account by ID
      tags:
      - Accounts
    put:
      consumes:
      - application/json
      description: Update an existing account's properties
      parameters:
      - description: Account ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Account update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AccountUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Account updated successfully
          schema:
            $ref: '#/definitions/handlers.AccountResponse'
        "400":
          description: Invalid request data or account ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update account
      tags:
      - Accounts
  /entitlements:
    get:
      consumes:
      - application/json
      description: Get a paginated list of entitlements for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for entitlement name or code
        example: '"premium"'
        in: query
        name: search
        type: string
      - description: Filter by environment ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: environment_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of entitlements retrieved successfully
          schema:
            $ref: '#/definitions/handlers.EntitlementListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List entitlements
      tags:
      - Entitlements
    post:
      consumes:
      - application/json
      description: Create a new entitlement for feature access control
      parameters:
      - description: Entitlement creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.EntitlementCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Entitlement created successfully
          schema:
            $ref: '#/definitions/handlers.EntitlementResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new entitlement
      tags:
      - Entitlements
  /entitlements/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an entitlement by ID (soft delete)
      parameters:
      - description: Entitlement ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Entitlement deleted successfully
        "400":
          description: Invalid entitlement ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Entitlement not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete entitlement
      tags:
      - Entitlements
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific entitlement
      parameters:
      - description: Entitlement ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Entitlement retrieved successfully
          schema:
            $ref: '#/definitions/handlers.EntitlementResponse'
        "400":
          description: Invalid entitlement ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Entitlement not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get entitlement by ID
      tags:
      - Entitlements
    put:
      consumes:
      - application/json
      description: Update an existing entitlement's properties
      parameters:
      - description: Entitlement ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Entitlement update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.EntitlementUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Entitlement updated successfully
          schema:
            $ref: '#/definitions/handlers.EntitlementResponse'
        "400":
          description: Invalid request data or entitlement ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Entitlement not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update entitlement
      tags:
      - Entitlements
  /groups:
    get:
      consumes:
      - application/json
      description: Get a paginated list of groups for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for group name
        example: '"development"'
        in: query
        name: search
        type: string
      - description: Filter by environment ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: environment_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of groups retrieved successfully
          schema:
            $ref: '#/definitions/handlers.GroupListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List groups
      tags:
      - Groups
    post:
      consumes:
      - application/json
      description: Create a new group for organizing users and resources
      parameters:
      - description: Group creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.GroupCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Group created successfully
          schema:
            $ref: '#/definitions/handlers.GroupResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new group
      tags:
      - Groups
  /groups/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a group by ID (soft delete)
      parameters:
      - description: Group ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Group deleted successfully
        "400":
          description: Invalid group ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Group not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete group
      tags:
      - Groups
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific group
      parameters:
      - description: Group ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Group retrieved successfully
          schema:
            $ref: '#/definitions/handlers.GroupResponse'
        "400":
          description: Invalid group ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Group not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get group by ID
      tags:
      - Groups
    put:
      consumes:
      - application/json
      description: Update an existing group's properties
      parameters:
      - description: Group ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Group update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.GroupUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Group updated successfully
          schema:
            $ref: '#/definitions/handlers.GroupResponse'
        "400":
          description: Invalid request data or group ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Group not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update group
      tags:
      - Groups
  /licenses:
    get:
      consumes:
      - application/json
      description: Get a paginated list of licenses for the authenticated account
        (alternative to ListLicensesHandler)
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for license key or name
        example: '"LIC-12345"'
        in: query
        name: search
        type: string
      - description: Filter by policy ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: policy_id
        type: string
      - description: Filter by product ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: product_id
        type: string
      - description: Filter by status
        example: '"ACTIVE"'
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of licenses retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List licenses (alternative endpoint)
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      deprecated: true
      description: Create a new license (legacy endpoint - use CreateLicense instead)
      parameters:
      - description: License creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.LicenseCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: License created successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new license (legacy)
      tags:
      - Licenses
  /licenses/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a license by ID (soft delete)
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: License deleted successfully
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete license
      tags:
      - Licenses
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific license (alternative
        to GetLicenseHandler)
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid license ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get license by ID (alternative endpoint)
      tags:
      - Licenses
    put:
      consumes:
      - application/json
      deprecated: true
      description: Update an existing license (legacy endpoint - use UpdateLicense
        instead)
      parameters:
      - description: License ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: License update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.LicenseUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: License updated successfully
          schema:
            $ref: '#/definitions/handlers.LicenseResponse'
        "400":
          description: Invalid request data or license ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update license (legacy)
      tags:
      - Licenses
  /licenses/{id}/actions/checkout:
    get:
      consumes:
      - application/json
      description: Generate and retrieve a signed license certificate for offline
        validation
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: License certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout license certificate
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Generate and retrieve a signed license certificate for offline
        validation
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: License certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: License not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout license certificate
      tags:
      - Licenses
  /licenses/{id}/actions/quick-validate:
    post:
      consumes:
      - application/json
      description: Performs a quick validation of a license by its ID or license key
        without detailed tracking
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Quick validate license by ID or key
      tags:
      - Licenses
  /licenses/{id}/actions/validate:
    post:
      consumes:
      - application/json
      description: Validates a license by its ID or license key with optional machine
        tracking
      parameters:
      - description: License ID or license key
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Machine fingerprint for validation
        example: '"fp-mac-********"'
        in: query
        name: machine_fingerprint
        type: string
      - description: Environment for validation
        example: '"production"'
        in: query
        name: environment
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation successful
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid license ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: License validation failed
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: License not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Validate license by ID or key
      tags:
      - Licenses
  /licenses/actions/validate-key:
    post:
      consumes:
      - application/json
      description: Validates a license using the license key provided in request body
      parameters:
      - description: Request with license key in meta.key field
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: License validation result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request format
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Validate license by key
      tags:
      - Licenses
  /licenses/info:
    get:
      consumes:
      - application/json
      description: Retrieves detailed information about a license without performing
        validation
      parameters:
      - description: License key to get info for
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License information retrieved successfully
          schema:
            $ref: '#/definitions/handlers.LicenseInfoResponse'
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get license information
      tags:
      - Licenses
  /licenses/invalidate-cache:
    post:
      consumes:
      - application/json
      description: Invalidate the cache for a specific license key
      parameters:
      - description: License key to invalidate cache for
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        type: string
      - description: Request body with license_key if not in query
        in: body
        name: request
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Cache invalidated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Invalidate license cache
      tags:
      - Licenses
  /licenses/quick-validate:
    get:
      consumes:
      - application/json
      description: Performs a quick validation of a license key without detailed tracking
      parameters:
      - description: License key to validate
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License is valid
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "403":
          description: License is invalid
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Quick license validation
      tags:
      - Licenses
  /licenses/validate:
    get:
      consumes:
      - application/json
      description: Validates a license key using query parameters
      parameters:
      - description: License key to validate
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      - description: Machine fingerprint for validation
        example: '"fp-mac-********"'
        in: query
        name: machine_fingerprint
        type: string
      - description: Environment for validation
        example: '"production"'
        in: query
        name: environment
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation successful
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Missing license key parameter
          schema:
            additionalProperties: true
            type: object
        "403":
          description: License validation failed
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Validate license via GET
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Validates a license key and returns detailed validation result
        including machine tracking
      parameters:
      - description: License validation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ValidateLicenseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: License validation successful
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Validate license
      tags:
      - Licenses
  /machines:
    get:
      consumes:
      - application/json
      description: Get a paginated list of machines for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for machine name or fingerprint
        example: '"dev-machine"'
        in: query
        name: search
        type: string
      - description: Filter by license ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: license_id
        type: string
      - description: Filter by policy ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: policy_id
        type: string
      - description: Filter by group ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: group_id
        type: string
      - description: Filter by owner ID
        example: '"550e8400-e29b-41d4-a716-*********003"'
        in: query
        name: owner_id
        type: string
      - description: Filter by status
        example: '"ACTIVE"'
        in: query
        name: status
        type: string
      - description: Filter by platform
        example: '"windows"'
        in: query
        name: platform
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of machines retrieved successfully
          schema:
            $ref: '#/definitions/handlers.MachineListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List machines
      tags:
      - Machines
    post:
      consumes:
      - application/json
      description: Create a new machine for license tracking
      parameters:
      - description: Machine creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.MachineCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Machine created successfully
          schema:
            $ref: '#/definitions/handlers.MachineResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new machine
      tags:
      - Machines
  /machines/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a machine by ID (soft delete)
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Machine deleted successfully
        "400":
          description: Invalid machine ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Machine not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete machine
      tags:
      - Machines
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific machine (supports
        both ID and fingerprint lookup)
      parameters:
      - description: Machine ID or fingerprint
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Machine retrieved successfully
          schema:
            $ref: '#/definitions/handlers.MachineResponse'
        "400":
          description: Invalid machine ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get machine by ID
      tags:
      - Machines
    put:
      consumes:
      - application/json
      description: Update an existing machine's properties
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Machine update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.MachineUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Machine updated successfully
          schema:
            $ref: '#/definitions/handlers.MachineResponse'
        "400":
          description: Invalid request data or machine ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update machine
      tags:
      - Machines
  /machines/{id}/actions/checkout:
    get:
      consumes:
      - application/json
      description: Generate and retrieve a signed machine certificate for offline
        validation
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: Machine certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout machine certificate
      tags:
      - Machines
    post:
      consumes:
      - application/json
      description: Generate and retrieve a signed machine certificate for offline
        validation
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Encrypt the certificate
        example: false
        in: query
        name: encrypt
        type: boolean
      - description: Signing algorithm
        example: '"ED25519_SIGN"'
        in: query
        name: algorithm
        type: string
      - description: Certificate TTL in seconds
        example: 3600
        in: query
        name: ttl
        type: integer
      - description: Additional data to include
        in: query
        name: include
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: Machine certificate generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Machine not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Checkout machine certificate
      tags:
      - Machines
  /machines/{id}/actions/heartbeats/ping:
    post:
      consumes:
      - application/json
      description: Send a heartbeat ping for a machine to indicate it's still active
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Heartbeat ping successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid machine ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Machine not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Send machine heartbeat ping
      tags:
      - Machines
  /machines/{id}/actions/heartbeats/reset:
    post:
      consumes:
      - application/json
      description: Reset the heartbeat status for a machine
      parameters:
      - description: Machine ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Heartbeat reset successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid machine ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Machine not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Reset machine heartbeat
      tags:
      - Machines
  /policies:
    get:
      consumes:
      - application/json
      description: Get a paginated list of policies for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for policy name
        example: '"standard"'
        in: query
        name: search
        type: string
      - description: Filter by product ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: product_id
        type: string
      - description: Filter by environment ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: environment_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of policies retrieved successfully
          schema:
            $ref: '#/definitions/handlers.PolicyListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List policies
      tags:
      - Policies
    post:
      consumes:
      - application/json
      description: Create a new policy for license management
      parameters:
      - description: Policy creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.PolicyCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Policy created successfully
          schema:
            $ref: '#/definitions/handlers.PolicyResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new policy
      tags:
      - Policies
  /policies/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a policy by ID (soft delete)
      parameters:
      - description: Policy ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Policy deleted successfully
        "400":
          description: Invalid policy ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Policy not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete policy
      tags:
      - Policies
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific policy
      parameters:
      - description: Policy ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Policy retrieved successfully
          schema:
            $ref: '#/definitions/handlers.PolicyResponse'
        "400":
          description: Invalid policy ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Policy not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get policy by ID
      tags:
      - Policies
    put:
      consumes:
      - application/json
      description: Update an existing policy's properties
      parameters:
      - description: Policy ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Policy update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.PolicyUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Policy updated successfully
          schema:
            $ref: '#/definitions/handlers.PolicyResponse'
        "400":
          description: Invalid request data or policy ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Policy not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update policy
      tags:
      - Policies
  /products:
    get:
      consumes:
      - application/json
      description: Get a paginated list of products for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: per_page
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for product name or code
        example: '"software"'
        in: query
        name: search
        type: string
      - description: Filter by distribution strategy
        example: '"licensed"'
        in: query
        name: distribution_strategy
        type: string
      - description: Filter by environment ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: query
        name: environment_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of products retrieved successfully
          schema:
            $ref: '#/definitions/handlers.ProductListResponse'
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List products
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Create a new product for license management
      parameters:
      - description: Product creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ProductCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Product created successfully
          schema:
            $ref: '#/definitions/handlers.ProductResponse'
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new product
      tags:
      - Products
  /products/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a product by ID (soft delete)
      parameters:
      - description: Product ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Product deleted successfully
        "400":
          description: Invalid product ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Product not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete product
      tags:
      - Products
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific product
      parameters:
      - description: Product ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Product retrieved successfully
          schema:
            $ref: '#/definitions/handlers.ProductResponse'
        "400":
          description: Invalid product ID format
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Product not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get product by ID
      tags:
      - Products
    put:
      consumes:
      - application/json
      description: Update an existing product's properties
      parameters:
      - description: Product ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Product update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ProductUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Product updated successfully
          schema:
            $ref: '#/definitions/handlers.ProductResponse'
        "400":
          description: Invalid request data or product ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Product not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update product
      tags:
      - Products
  /webhook-endpoints:
    get:
      consumes:
      - application/json
      description: Get a paginated list of webhook endpoints for the authenticated
        account
      parameters:
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 25, max: 100)'
        example: 25
        in: query
        name: limit
        type: integer
      - description: 'Sort field (default: created_at)'
        example: '"created_at"'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        example: '"DESC"'
        in: query
        name: sort_order
        type: string
      - description: Search term for webhook endpoint name or URL
        example: '"production"'
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of webhook endpoints retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List webhook endpoints
      tags:
      - Webhook Endpoints
    post:
      consumes:
      - application/json
      description: Create a new webhook endpoint for receiving event notifications
      parameters:
      - description: Webhook endpoint creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.WebhookEndpointRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Webhook endpoint created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new webhook endpoint
      tags:
      - Webhook Endpoints
  /webhook-endpoints/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a webhook endpoint by ID (soft delete)
      parameters:
      - description: Webhook Endpoint ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Webhook endpoint deleted successfully
        "400":
          description: Invalid webhook endpoint ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Webhook endpoint not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete webhook endpoint
      tags:
      - Webhook Endpoints
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific webhook endpoint
      parameters:
      - description: Webhook Endpoint ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Webhook endpoint retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid webhook endpoint ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Webhook endpoint not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get webhook endpoint by ID
      tags:
      - Webhook Endpoints
    put:
      consumes:
      - application/json
      description: Update an existing webhook endpoint's properties
      parameters:
      - description: Webhook Endpoint ID
        example: '"550e8400-e29b-41d4-a716-************"'
        in: path
        name: id
        required: true
        type: string
      - description: Webhook endpoint update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.WebhookEndpointRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Webhook endpoint updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data or webhook endpoint ID
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Webhook endpoint not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update webhook endpoint
      tags:
      - Webhook Endpoints
securityDefinitions:
  ApiKeyAuth:
    description: API key for authentication
    in: header
    name: X-API-Key
    type: apiKey
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
  LicenseKeyAuth:
    description: License key for validation endpoints
    in: header
    name: X-License-Key
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization endpoints
  name: Authentication
- description: License management and validation
  name: Licenses
- description: Account management
  name: Accounts
- description: Product management
  name: Products
- description: Policy configuration
  name: Policies
- description: Machine registration and tracking
  name: Machines
- description: User management
  name: Users
- description: System health and monitoring
  name: Health
- description: System metrics and analytics
  name: Metrics
