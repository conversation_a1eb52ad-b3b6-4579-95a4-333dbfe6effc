{"swagger": "2.0", "info": {"description": "Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.", "title": "GoKeys License Management API", "termsOfService": "https://gokeys.com/terms", "contact": {"name": "GoKeys API Support", "url": "https://gokeys.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/account": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve information about the currently authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Get current account", "responses": {"200": {"description": "Current account retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update the currently authenticated account's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Update current account", "parameters": [{"description": "Account update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountUpdateRequest"}}], "responses": {"200": {"description": "Current account updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all accounts (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "List accounts", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}], "responses": {"200": {"description": "List of accounts retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountListResponse"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"description": "Create a new account for license management", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Create a new account", "parameters": [{"description": "Account creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountCreateRequest"}}], "responses": {"201": {"description": "Account created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{account_id}/licenses": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of licenses for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "List licenses", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 20, "description": "Page size (default: 20, max: 100)", "name": "page_size", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "List of licenses retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{account_id}/licenses/actions/validate-key": {"post": {"description": "Validates a license using the license key provided in request body", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license by key", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"description": "Request with license key in meta.key field", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": true}}], "responses": {"200": {"description": "License validation result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request format", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{account_id}/licenses/{id}/actions/check-out": {"get": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed license certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Checkout license certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "License certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed license certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Checkout license certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "License certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{account_id}/licenses/{id}/actions/validate": {"get": {"security": [{"BearerAuth": []}], "description": "Performs a quick validation of a license by its ID or license key without detailed tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Quick validate license by ID or key", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}], "responses": {"200": {"description": "License validation result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Validates a license by its ID or license key with optional machine tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license by ID or key", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "string", "example": "\"fp-mac-********\"", "description": "Machine fingerprint for validation", "name": "machine_fingerprint", "in": "query"}, {"type": "string", "example": "\"production\"", "description": "Environment for validation", "name": "environment", "in": "query"}], "responses": {"200": {"description": "License validation successful", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "403": {"description": "License validation failed", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/accounts/{account_id}/machines/{id}/actions/check-out": {"get": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed machine certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Checkout machine certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "Machine certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed machine certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Checkout machine certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "Machine certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{account_id}/machines/{id}/actions/ping": {"post": {"security": [{"BearerAuth": []}], "description": "Send a heartbeat ping for a machine to indicate it's still active", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Send machine heartbeat ping", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}], "responses": {"200": {"description": "Heartbeat ping successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid machine ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Machine not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/accounts/{account_id}/machines/{id}/actions/reset": {"post": {"security": [{"BearerAuth": []}], "description": "Reset the heartbeat status for a machine", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Reset machine heartbeat", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID (for account-scoped routes)", "name": "account_id", "in": "path"}], "responses": {"200": {"description": "Heartbeat reset successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid machine ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Machine not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/accounts/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Get account by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Account retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "400": {"description": "Invalid account ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing account's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Update account", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID", "name": "id", "in": "path", "required": true}, {"description": "Account update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountUpdateRequest"}}], "responses": {"200": {"description": "Account updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "400": {"description": "Invalid request data or account ID", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete an account by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Delete account", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Account deleted successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid account ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/entitlements": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of entitlements for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Entitlements"], "summary": "List entitlements", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"premium\"", "description": "Search term for entitlement name or code", "name": "search", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by environment ID", "name": "environment_id", "in": "query"}], "responses": {"200": {"description": "List of entitlements retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new entitlement for feature access control", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Entitlements"], "summary": "Create a new entitlement", "parameters": [{"description": "Entitlement creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementCreateRequest"}}], "responses": {"201": {"description": "Entitlement created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/entitlements/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific entitlement", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Entitlements"], "summary": "Get entitlement by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Entitlement ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Entitlement retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementResponse"}}, "400": {"description": "Invalid entitlement ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Entitlement not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing entitlement's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Entitlements"], "summary": "Update entitlement", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Entitlement ID", "name": "id", "in": "path", "required": true}, {"description": "Entitlement update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementUpdateRequest"}}], "responses": {"200": {"description": "Entitlement updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementResponse"}}, "400": {"description": "Invalid request data or entitlement ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Entitlement not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete an entitlement by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Entitlements"], "summary": "Delete entitlement", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Entitlement ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Entitlement deleted successfully"}, "400": {"description": "Invalid entitlement ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Entitlement not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/groups": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of groups for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Groups"], "summary": "List groups", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"development\"", "description": "Search term for group name", "name": "search", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by environment ID", "name": "environment_id", "in": "query"}], "responses": {"200": {"description": "List of groups retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new group for organizing users and resources", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Groups"], "summary": "Create a new group", "parameters": [{"description": "Group creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupCreateRequest"}}], "responses": {"201": {"description": "Group created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/groups/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific group", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Groups"], "summary": "Get group by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Group ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Group retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupResponse"}}, "400": {"description": "Invalid group ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Group not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing group's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Groups"], "summary": "Update group", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Group ID", "name": "id", "in": "path", "required": true}, {"description": "Group update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupUpdateRequest"}}], "responses": {"200": {"description": "Group updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupResponse"}}, "400": {"description": "Invalid request data or group ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Group not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a group by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Groups"], "summary": "Delete group", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Group ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Group deleted successfully"}, "400": {"description": "Invalid group ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Group not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/licenses": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of licenses for the authenticated account (alternative to ListLicensesHandler)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "List licenses (alternative endpoint)", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"LIC-12345\"", "description": "Search term for license key or name", "name": "search", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by policy ID", "name": "policy_id", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by product ID", "name": "product_id", "in": "query"}, {"type": "string", "example": "\"ACTIVE\"", "description": "Filter by status", "name": "status", "in": "query"}], "responses": {"200": {"description": "List of licenses retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new license (legacy endpoint - use CreateLicense instead)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Create a new license (legacy)", "deprecated": true, "parameters": [{"description": "License creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseCreateRequest"}}], "responses": {"201": {"description": "License created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/actions/validate-key": {"post": {"description": "Validates a license using the license key provided in request body", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license by key", "parameters": [{"description": "Request with license key in meta.key field", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": true}}], "responses": {"200": {"description": "License validation result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request format", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/info": {"get": {"description": "Retrieves detailed information about a license without performing validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Get license information", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to get info for", "name": "license_key", "in": "query", "required": true}], "responses": {"200": {"description": "License information retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseInfoResponse"}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/invalidate-cache": {"post": {"security": [{"BearerAuth": []}], "description": "Invalidate the cache for a specific license key", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Invalidate license cache", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to invalidate cache for", "name": "license_key", "in": "query"}, {"description": "Request body with license_key if not in query", "name": "request", "in": "body", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "<PERSON><PERSON> invalidated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/quick-validate": {"get": {"description": "Performs a quick validation of a license key without detailed tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Quick license validation", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to validate", "name": "license_key", "in": "query", "required": true}], "responses": {"200": {"description": "License is valid", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "License is invalid", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/validate": {"get": {"description": "Validates a license key using query parameters", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license via GET", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to validate", "name": "license_key", "in": "query", "required": true}, {"type": "string", "example": "\"fp-mac-********\"", "description": "Machine fingerprint for validation", "name": "machine_fingerprint", "in": "query"}, {"type": "string", "example": "\"production\"", "description": "Environment for validation", "name": "environment", "in": "query"}], "responses": {"200": {"description": "License validation successful", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ValidateLicenseResponse"}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "License validation failed", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"description": "Validates a license key and returns detailed validation result including machine tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license", "parameters": [{"description": "License validation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ValidateLicenseRequest"}}], "responses": {"200": {"description": "License validation successful", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/licenses/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific license (alternative to GetLicenseHandler)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Get license by ID (alternative endpoint)", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "License retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseResponse"}}, "400": {"description": "Invalid license ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing license (legacy endpoint - use UpdateLicense instead)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Update license (legacy)", "deprecated": true, "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}, {"description": "License update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseUpdateRequest"}}], "responses": {"200": {"description": "License updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseResponse"}}, "400": {"description": "Invalid request data or license ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a license by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Delete license", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "License deleted successfully"}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/licenses/{id}/actions/checkout": {"get": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed license certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Checkout license certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "License certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed license certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Checkout license certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "License certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/{id}/actions/quick-validate": {"post": {"security": [{"BearerAuth": []}], "description": "Performs a quick validation of a license by its ID or license key without detailed tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Quick validate license by ID or key", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "License validation result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/licenses/{id}/actions/validate": {"post": {"security": [{"BearerAuth": []}], "description": "Validates a license by its ID or license key with optional machine tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license by ID or key", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID or license key", "name": "id", "in": "path", "required": true}, {"type": "string", "example": "\"fp-mac-********\"", "description": "Machine fingerprint for validation", "name": "machine_fingerprint", "in": "query"}, {"type": "string", "example": "\"production\"", "description": "Environment for validation", "name": "environment", "in": "query"}], "responses": {"200": {"description": "License validation successful", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "403": {"description": "License validation failed", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/machines": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of machines for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "List machines", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"dev-machine\"", "description": "Search term for machine name or fingerprint", "name": "search", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by license ID", "name": "license_id", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by policy ID", "name": "policy_id", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by group ID", "name": "group_id", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-***********3\"", "description": "Filter by owner ID", "name": "owner_id", "in": "query"}, {"type": "string", "example": "\"ACTIVE\"", "description": "Filter by status", "name": "status", "in": "query"}, {"type": "string", "example": "\"windows\"", "description": "Filter by platform", "name": "platform", "in": "query"}], "responses": {"200": {"description": "List of machines retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new machine for license tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Create a new machine", "parameters": [{"description": "Machine creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineCreateRequest"}}], "responses": {"201": {"description": "Machine created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/machines/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific machine (supports both ID and fingerprint lookup)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Get machine by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID or fingerprint", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Machine retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineResponse"}}, "400": {"description": "Invalid machine ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing machine's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Update machine", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"description": "Machine update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineUpdateRequest"}}], "responses": {"200": {"description": "Machine updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineResponse"}}, "400": {"description": "Invalid request data or machine ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a machine by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Delete machine", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Machine deleted successfully"}, "400": {"description": "Invalid machine ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Machine not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/machines/{id}/actions/checkout": {"get": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed machine certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Checkout machine certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "Machine certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Generate and retrieve a signed machine certificate for offline validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Checkout machine certificate", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}, {"type": "boolean", "example": false, "description": "Encrypt the certificate", "name": "encrypt", "in": "query"}, {"type": "string", "example": "\"ED25519_SIGN\"", "description": "Signing algorithm", "name": "algorithm", "in": "query"}, {"type": "integer", "example": 3600, "description": "Certificate TTL in seconds", "name": "ttl", "in": "query"}, {"type": "array", "description": "Additional data to include", "name": "include", "in": "query"}], "responses": {"200": {"description": "Machine certificate generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request parameters", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Machine not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/machines/{id}/actions/heartbeats/ping": {"post": {"security": [{"BearerAuth": []}], "description": "Send a heartbeat ping for a machine to indicate it's still active", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Send machine heartbeat ping", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Heartbeat ping successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid machine ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Machine not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/machines/{id}/actions/heartbeats/reset": {"post": {"security": [{"BearerAuth": []}], "description": "Reset the heartbeat status for a machine", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Machines"], "summary": "Reset machine heartbeat", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Machine ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Heartbeat reset successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid machine ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Machine not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/policies": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of policies for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Policies"], "summary": "List policies", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"standard\"", "description": "Search term for policy name", "name": "search", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by product ID", "name": "product_id", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by environment ID", "name": "environment_id", "in": "query"}], "responses": {"200": {"description": "List of policies retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new policy for license management", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Policies"], "summary": "Create a new policy", "parameters": [{"description": "Policy creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyCreateRequest"}}], "responses": {"201": {"description": "Policy created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/policies/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific policy", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Policies"], "summary": "Get policy by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Policy ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Policy retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyResponse"}}, "400": {"description": "Invalid policy ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Policy not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing policy's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Policies"], "summary": "Update policy", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Policy ID", "name": "id", "in": "path", "required": true}, {"description": "Policy update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyUpdateRequest"}}], "responses": {"200": {"description": "Policy updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyResponse"}}, "400": {"description": "Invalid request data or policy ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Policy not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a policy by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Policies"], "summary": "Delete policy", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Policy ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Policy deleted successfully"}, "400": {"description": "Invalid policy ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Policy not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/products": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of products for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "List products", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"software\"", "description": "Search term for product name or code", "name": "search", "in": "query"}, {"type": "string", "example": "\"licensed\"", "description": "Filter by distribution strategy", "name": "distribution_strategy", "in": "query"}, {"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Filter by environment ID", "name": "environment_id", "in": "query"}], "responses": {"200": {"description": "List of products retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new product for license management", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "Create a new product", "parameters": [{"description": "Product creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductCreateRequest"}}], "responses": {"201": {"description": "Product created successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/products/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific product", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "Get product by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Product ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Product retrieved successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductResponse"}}, "400": {"description": "Invalid product ID format", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Product not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing product's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "Update product", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Product ID", "name": "id", "in": "path", "required": true}, {"description": "Product update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductUpdateRequest"}}], "responses": {"200": {"description": "Product updated successfully", "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductResponse"}}, "400": {"description": "Invalid request data or product ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Product not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a product by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "Delete product", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Product ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Product deleted successfully"}, "400": {"description": "Invalid product ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Product not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/webhook-endpoints": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of webhook endpoints for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Webhook Endpoints"], "summary": "List webhook endpoints", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "example": "\"production\"", "description": "Search term for webhook endpoint name or URL", "name": "search", "in": "query"}], "responses": {"200": {"description": "List of webhook endpoints retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new webhook endpoint for receiving event notifications", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Webhook Endpoints"], "summary": "Create a new webhook endpoint", "parameters": [{"description": "Webhook endpoint creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.WebhookEndpointRequest"}}], "responses": {"201": {"description": "Webhook endpoint created successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request data", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}, "/webhook-endpoints/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific webhook endpoint", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Webhook Endpoints"], "summary": "Get webhook endpoint by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Webhook Endpoint ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Webhook endpoint retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid webhook endpoint ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Webhook endpoint not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing webhook endpoint's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Webhook Endpoints"], "summary": "Update webhook endpoint", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Webhook Endpoint ID", "name": "id", "in": "path", "required": true}, {"description": "Webhook endpoint update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_adapters_http_handlers.WebhookEndpointRequest"}}], "responses": {"200": {"description": "Webhook endpoint updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request data or webhook endpoint ID", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Webhook endpoint not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a webhook endpoint by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Webhook Endpoints"], "summary": "Delete webhook endpoint", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Webhook Endpoint ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Webhook endpoint deleted successfully"}, "400": {"description": "Invalid webhook endpoint ID format", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "404": {"description": "Webhook endpoint not found", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse"}}}}}}, "definitions": {"github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorCode": {"type": "string", "enum": ["UNAUTHORIZED", "INVALID_TOKEN", "TOKEN_EXPIRED", "INSUFFICIENT_SCOPE", "FORBIDDEN", "INSUFFICIENT_PERMISSIONS", "VALIDATION_FAILED", "INVALID_REQUEST", "MISSING_PARAMETER", "INVALID_PARAMETER", "NOT_FOUND", "RESOURCE_NOT_FOUND", "CONFLICT", "RESOURCE_EXISTS", "LICENSE_EXPIRED", "LICENSE_SUSPENDED", "LICENSE_INVALID", "LICENSE_NOT_FOUND", "MACHINE_HEARTBEAT_DEAD", "TOO_MANY_MACHINES", "NO_MACHINE", "CHECKOUT_ALGORITHM_INVALID", "CHECKOUT_INCLUDE_INVALID", "CHECKOUT_TTL_INVALID", "INTERNAL_ERROR", "SERVICE_UNAVAILABLE", "RATE_LIMIT_EXCEEDED"], "x-enum-varnames": ["ErrorCodeUnauthorized", "ErrorCodeInvalidToken", "ErrorCodeTokenExpired", "ErrorCodeInsufficientScope", "ErrorCodeForbidden", "ErrorCodeInsufficientPermissions", "ErrorCodeValidationFailed", "ErrorCodeInvalidRequest", "ErrorCodeMissingParameter", "ErrorCodeInvalidParameter", "ErrorCodeNotFound", "ErrorCodeResourceNotFound", "ErrorCodeConflict", "ErrorCodeResourceExists", "ErrorCodeLicenseExpired", "ErrorCodeLicenseSuspended", "ErrorCodeLicenseInvalid", "ErrorCodeLicenseNotFound", "ErrorCodeMachineHeartbeatDead", "ErrorCodeTooManyMachines", "ErrorCodeNoMachine", "ErrorCodeCheckoutAlgorithmInvalid", "ErrorCodeCheckoutIncludeInvalid", "ErrorCodeCheckoutTTLInvalid", "ErrorCodeInternalError", "ErrorCodeServiceUnavailable", "ErrorCodeRateLimitExceeded"]}, "github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorDetail": {"type": "object", "properties": {"code": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorCode"}, "detail": {"type": "string"}, "meta": {"type": "object", "additionalProperties": true}, "source": {"type": "object", "additionalProperties": true}, "title": {"type": "string"}}}, "github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorResponse": {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/github_com_gokeys_gokeys_internal_adapters_http_responses.ErrorDetail"}}}}, "internal_adapters_http_handlers.AccountCreateRequest": {"description": "Request payload for creating a new account", "type": "object", "required": ["email", "name", "slug"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Acme Corporation"}, "slug": {"type": "string", "maxLength": 255, "minLength": 1, "example": "acme-corp"}}}, "internal_adapters_http_handlers.AccountListResponse": {"description": "Paginated list of accounts", "type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.AccountResponse"}}, "pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}}}, "internal_adapters_http_handlers.AccountResponse": {"description": "Account information returned by the API", "type": "object", "properties": {"created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "email": {"type": "string", "example": "<EMAIL>"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Acme Corporation"}, "slug": {"type": "string", "example": "acme-corp"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}}}, "internal_adapters_http_handlers.AccountUpdateRequest": {"description": "Request payload for updating an account", "type": "object", "properties": {"metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Acme Corporation"}, "slug": {"type": "string", "maxLength": 255, "minLength": 1, "example": "updated-acme"}}}, "internal_adapters_http_handlers.EntitlementCreateRequest": {"description": "Request payload for creating a new entitlement", "type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 255, "minLength": 1, "example": "premium-features"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Premium Features"}}}, "internal_adapters_http_handlers.EntitlementListResponse": {"description": "Paginated list of entitlements", "type": "object", "properties": {"entitlements": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.EntitlementResponse"}}, "pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}}}, "internal_adapters_http_handlers.EntitlementResponse": {"description": "Entitlement information returned by the API", "type": "object", "properties": {"code": {"type": "string", "example": "premium-features"}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Premium Features"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}}}, "internal_adapters_http_handlers.EntitlementUpdateRequest": {"description": "Request payload for updating an entitlement", "type": "object", "properties": {"code": {"type": "string", "maxLength": 255, "minLength": 1, "example": "updated-premium"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Premium Features"}}}, "internal_adapters_http_handlers.GroupCreateRequest": {"description": "Request payload for creating a new group", "type": "object", "required": ["name"], "properties": {"environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "max_licenses": {"type": "integer", "minimum": 0, "example": 5}, "max_machines": {"type": "integer", "minimum": 0, "example": 20}, "max_users": {"type": "integer", "minimum": 0, "example": 10}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Development Team"}}}, "internal_adapters_http_handlers.GroupListResponse": {"description": "Paginated list of groups", "type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.GroupResponse"}}, "pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}}}, "internal_adapters_http_handlers.GroupResponse": {"description": "Group information returned by the API", "type": "object", "properties": {"created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "max_licenses": {"type": "integer", "example": 5}, "max_machines": {"type": "integer", "example": 20}, "max_users": {"type": "integer", "example": 10}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Development Team"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}}}, "internal_adapters_http_handlers.GroupUpdateRequest": {"description": "Request payload for updating a group", "type": "object", "properties": {"max_licenses": {"type": "integer", "minimum": 0, "example": 8}, "max_machines": {"type": "integer", "minimum": 0, "example": 30}, "max_users": {"type": "integer", "minimum": 0, "example": 15}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Development Team"}}}, "internal_adapters_http_handlers.LicenseCreateRequest": {"description": "Request payload for creating a new license", "type": "object", "required": ["name", "policy_id"], "properties": {"expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Enterprise License"}, "policy_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "user_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}}}, "internal_adapters_http_handlers.LicenseInfoResponse": {"description": "License information returned by info endpoints", "type": "object", "properties": {"account_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "claims": {"type": "object", "additionalProperties": true}, "expired": {"type": "boolean", "example": false}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "format": {"type": "string", "example": "json"}, "product_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "ACTIVE"}, "suspended": {"type": "boolean", "example": false}}}, "internal_adapters_http_handlers.LicenseListResponse": {"description": "Paginated list of licenses", "type": "object", "properties": {"licenses": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.LicenseResponse"}}, "pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}}}, "internal_adapters_http_handlers.LicenseResponse": {"description": "License information returned by the API", "type": "object", "properties": {"created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Enterprise License"}, "policy_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "ACTIVE"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}, "user_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}}}, "internal_adapters_http_handlers.LicenseUpdateRequest": {"description": "Request payload for updating a license", "type": "object", "properties": {"expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Enterprise License"}, "status": {"type": "string", "enum": ["ACTIVE", "SUSPENDED", "EXPIRED"], "example": "ACTIVE"}}}, "internal_adapters_http_handlers.MachineCreateRequest": {"description": "Request payload for creating a new machine", "type": "object", "required": ["fingerprint", "license_id"], "properties": {"cores": {"type": "integer", "minimum": 1, "example": 8}, "fingerprint": {"type": "string", "maxLength": 255, "minLength": 1, "example": "fp-mac-********"}, "hostname": {"type": "string", "example": "dev-machine-01"}, "ip": {"type": "string", "example": "*************"}, "license_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Development Machine"}, "platform": {"type": "string", "example": "windows"}}}, "internal_adapters_http_handlers.MachineListResponse": {"description": "Paginated list of machines", "type": "object", "properties": {"machines": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.MachineResponse"}}, "pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}}}, "internal_adapters_http_handlers.MachineResponse": {"description": "Machine information returned by the API", "type": "object", "properties": {"cores": {"type": "integer", "example": 8}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-***********5"}, "fingerprint": {"type": "string", "example": "fp-mac-********"}, "group_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-***********3"}, "hostname": {"type": "string", "example": "dev-machine-01"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "ip": {"type": "string", "example": "*************"}, "last_seen": {"type": "string", "example": "2025-07-15T14:30:00Z"}, "license_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Development Machine"}, "owner_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-***********4"}, "platform": {"type": "string", "example": "windows"}, "policy_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "ACTIVE"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}}}, "internal_adapters_http_handlers.MachineUpdateRequest": {"description": "Request payload for updating a machine", "type": "object", "properties": {"cores": {"type": "integer", "minimum": 1, "example": 16}, "fingerprint": {"type": "string", "maxLength": 255, "minLength": 1, "example": "fp-mac-87654321"}, "hostname": {"type": "string", "example": "updated-dev-machine"}, "ip": {"type": "string", "example": "*************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Updated Development Machine"}, "platform": {"type": "string", "example": "linux"}}}, "internal_adapters_http_handlers.PaginationInfo": {"description": "Pagination information for list responses", "type": "object", "properties": {"page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 150}, "total_pages": {"type": "integer", "example": 8}}}, "internal_adapters_http_handlers.PolicyCreateRequest": {"description": "Request payload for creating a new policy", "type": "object", "required": ["name", "product_id"], "properties": {"check_in_interval": {"type": "string", "example": "daily"}, "check_in_interval_count": {"type": "integer", "minimum": 0, "example": 1}, "description": {"type": "string", "example": "A standard policy for regular licenses"}, "duration": {"type": "integer", "minimum": 0, "example": 365}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "expiration_strategy": {"type": "string", "enum": ["RESTRICT_ACCESS", "REVOKE_ACCESS", "MAINTAIN_ACCESS"], "example": "RESTRICT_ACCESS"}, "floating": {"type": "boolean", "example": false}, "heartbeat_duration": {"type": "integer", "minimum": 0, "example": 3600}, "machine_uniqueness_strategy": {"type": "string", "enum": ["UNIQUE_PER_ACCOUNT", "UNIQUE_PER_PRODUCT", "UNIQUE_PER_POLICY", "UNIQUE_PER_LICENSE"], "example": "UNIQUE_PER_LICENSE"}, "max_activations": {"type": "integer", "minimum": 0, "example": 10}, "max_cores": {"type": "integer", "minimum": 0, "example": 8}, "max_deactivations": {"type": "integer", "minimum": 0, "example": 5}, "max_machines": {"description": "Machine limits", "type": "integer", "minimum": 0, "example": 5}, "max_processes": {"type": "integer", "minimum": 0, "example": 10}, "max_users": {"type": "integer", "minimum": 0, "example": 3}, "max_uses": {"type": "integer", "minimum": 0, "example": 100}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Standard License Policy"}, "overage_strategy": {"type": "string", "enum": ["NO_OVERAGE", "ALWAYS_ALLOW_OVERAGE", "ALLOW_1_25X_OVERAGE", "ALLOW_1_5X_OVERAGE", "ALLOW_2X_OVERAGE"], "example": "NO_OVERAGE"}, "product_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "protected": {"type": "boolean", "example": false}, "require_check_in": {"description": "Check-in settings", "type": "boolean", "example": false}, "require_heartbeat": {"type": "boolean", "example": true}, "scheme": {"description": "Advanced settings (simplified)", "type": "string", "enum": ["ED25519_SIGN", "RSA_PKCS1_SIGN", "RSA_PSS_SIGN"], "example": "ED25519_SIGN"}, "strict": {"type": "boolean", "example": true}, "use_pool": {"description": "Pool and activation limits", "type": "boolean", "example": false}}}, "internal_adapters_http_handlers.PolicyListResponse": {"description": "Paginated list of policies", "type": "object", "properties": {"pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.PolicyResponse"}}}}, "internal_adapters_http_handlers.PolicyResponse": {"description": "Policy information returned by the API", "type": "object", "properties": {"check_in_interval": {"type": "string", "example": "daily"}, "check_in_interval_count": {"type": "integer", "example": 1}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "description": {"type": "string", "example": "A standard policy for regular licenses"}, "duration": {"type": "integer", "example": 365}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "expiration_strategy": {"type": "string", "example": "RESTRICT_ACCESS"}, "floating": {"type": "boolean", "example": false}, "heartbeat_duration": {"type": "integer", "example": 3600}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "machine_uniqueness_strategy": {"type": "string", "example": "UNIQUE_PER_LICENSE"}, "max_activations": {"type": "integer", "example": 10}, "max_cores": {"type": "integer", "example": 8}, "max_deactivations": {"type": "integer", "example": 5}, "max_machines": {"type": "integer", "example": 5}, "max_processes": {"type": "integer", "example": 10}, "max_users": {"type": "integer", "example": 3}, "max_uses": {"type": "integer", "example": 100}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Standard License Policy"}, "overage_strategy": {"type": "string", "example": "NO_OVERAGE"}, "product_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "protected": {"type": "boolean", "example": false}, "require_check_in": {"type": "boolean", "example": false}, "require_heartbeat": {"type": "boolean", "example": true}, "scheme": {"type": "string", "example": "ED25519_SIGN"}, "strict": {"type": "boolean", "example": true}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}, "use_pool": {"type": "boolean", "example": false}}}, "internal_adapters_http_handlers.PolicyUpdateRequest": {"description": "Request payload for updating a policy", "type": "object", "properties": {"check_in_interval": {"type": "string", "example": "weekly"}, "check_in_interval_count": {"type": "integer", "minimum": 0, "example": 2}, "description": {"type": "string", "example": "An updated standard policy for regular licenses"}, "duration": {"type": "integer", "minimum": 0, "example": 730}, "expiration_strategy": {"type": "string", "example": "REVOKE_ACCESS"}, "floating": {"type": "boolean", "example": true}, "heartbeat_duration": {"type": "integer", "minimum": 0, "example": 7200}, "machine_uniqueness_strategy": {"type": "string", "example": "UNIQUE_PER_POLICY"}, "max_activations": {"type": "integer", "minimum": 0, "example": 20}, "max_cores": {"type": "integer", "minimum": 0, "example": 16}, "max_deactivations": {"type": "integer", "minimum": 0, "example": 10}, "max_machines": {"description": "Machine limits", "type": "integer", "minimum": 0, "example": 10}, "max_processes": {"type": "integer", "minimum": 0, "example": 20}, "max_users": {"type": "integer", "minimum": 0, "example": 5}, "max_uses": {"type": "integer", "minimum": 0, "example": 200}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Standard License Policy"}, "overage_strategy": {"type": "string", "example": "ALLOW_1_25X_OVERAGE"}, "protected": {"type": "boolean", "example": true}, "require_check_in": {"description": "Check-in settings", "type": "boolean", "example": true}, "require_heartbeat": {"type": "boolean", "example": false}, "scheme": {"description": "Advanced settings", "type": "string", "enum": ["ED25519_SIGN", "RSA_PKCS1_SIGN", "RSA_PSS_SIGN"], "example": "RSA_PKCS1_SIGN"}, "strict": {"type": "boolean", "example": false}, "use_pool": {"description": "Pool and activation limits", "type": "boolean", "example": true}}}, "internal_adapters_http_handlers.ProductCreateRequest": {"description": "Request payload for creating a new product", "type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 255, "minLength": 1, "example": "my-software"}, "description": {"type": "string", "example": "A comprehensive software solution"}, "distribution_strategy": {"type": "string", "enum": ["licensed", "open"], "example": "licensed"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "My Software Product"}, "platforms": {"type": "array", "items": {"type": "string"}, "example": ["windows", "macos", "linux"]}, "url": {"type": "string", "example": "https://example.com/product"}}}, "internal_adapters_http_handlers.ProductListResponse": {"description": "Paginated list of products", "type": "object", "properties": {"pagination": {"$ref": "#/definitions/internal_adapters_http_handlers.PaginationInfo"}, "products": {"type": "array", "items": {"$ref": "#/definitions/internal_adapters_http_handlers.ProductResponse"}}}}, "internal_adapters_http_handlers.ProductResponse": {"description": "Product information returned by the API", "type": "object", "properties": {"code": {"type": "string", "example": "my-software"}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "description": {"type": "string", "example": "A comprehensive software solution"}, "distribution_strategy": {"type": "string", "example": "licensed"}, "environment_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "My Software Product"}, "platforms": {"type": "array", "items": {"type": "string"}, "example": ["windows", "macos", "linux"]}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}, "url": {"type": "string", "example": "https://example.com/product"}}}, "internal_adapters_http_handlers.ProductUpdateRequest": {"description": "Request payload for updating a product", "type": "object", "properties": {"code": {"type": "string", "maxLength": 255, "minLength": 1, "example": "updated-software"}, "description": {"type": "string", "example": "An updated comprehensive software solution"}, "distribution_strategy": {"type": "string", "enum": ["licensed", "open"], "example": "licensed"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Software Product"}, "platforms": {"type": "array", "items": {"type": "string"}, "example": ["windows", "macos", "linux", "android"]}, "url": {"type": "string", "example": "https://example.com/updated-product"}}}, "internal_adapters_http_handlers.ValidateLicenseRequest": {"description": "Request payload for validating a license", "type": "object", "required": ["license_key"], "properties": {"environment": {"type": "string", "example": "production"}, "license_key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "machine_fingerprint": {"type": "string", "example": "fp-mac-********"}, "machine_info": {"type": "object", "additionalProperties": true}}}, "internal_adapters_http_handlers.ValidateLicenseResponse": {"description": "Response payload for license validation", "type": "object", "properties": {"account": {}, "cache_hit": {"type": "boolean", "example": false}, "claims": {"type": "object", "additionalProperties": true}, "errors": {"type": "array", "items": {"type": "string"}}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "license": {}, "machines_allowed": {"type": "integer", "example": 5}, "machines_used": {"type": "integer", "example": 2}, "policy": {}, "valid": {"type": "boolean", "example": true}, "validation_time": {"type": "string", "example": "2025-07-12T16:15:30Z"}, "warnings": {"type": "array", "items": {"type": "string"}}}}, "internal_adapters_http_handlers.WebhookEndpointRequest": {"description": "Request payload for webhook endpoint operations", "type": "object", "properties": {"data": {"type": "object", "required": ["type"], "properties": {"attributes": {"type": "object", "required": ["url"], "properties": {"api_version": {"type": "string", "example": "v1"}, "description": {"type": "string", "example": "Webhook for production environment"}, "enabled": {"type": "boolean", "example": true}, "events": {"type": "array", "items": {"type": "string"}, "example": ["license.created", "license.updated"]}, "max_retries": {"type": "integer", "example": 3}, "name": {"type": "string", "example": "Production Webhook"}, "retry_delay": {"type": "integer", "example": 5}, "signature_algorithm": {"type": "string", "example": "sha256"}, "url": {"type": "string", "example": "https://api.example.com/webhooks"}}}, "relationships": {"type": "object", "properties": {"environment": {"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "type": {"type": "string", "example": "environments"}}}}}}}, "type": {"type": "string", "example": "webhook-endpoints"}}}}}}, "securityDefinitions": {"ApiKeyAuth": {"description": "API key for authentication", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}, "BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "LicenseKeyAuth": {"description": "License key for validation endpoints", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-License-Key", "in": "header"}}, "tags": [{"description": "Authentication and authorization endpoints", "name": "Authentication"}, {"description": "License management and validation", "name": "Licenses"}, {"description": "Account management", "name": "Accounts"}, {"description": "Product management", "name": "Products"}, {"description": "Policy configuration", "name": "Policies"}, {"description": "Machine registration and tracking", "name": "Machines"}, {"description": "User management", "name": "Users"}, {"description": "System health and monitoring", "name": "Health"}, {"description": "System metrics and analytics", "name": "Metrics"}]}