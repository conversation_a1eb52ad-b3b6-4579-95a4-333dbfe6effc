{"swagger": "2.0", "info": {"description": "Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.", "title": "GoKeys License Management API", "termsOfService": "https://gokeys.com/terms", "contact": {"name": "GoKeys API Support", "url": "https://gokeys.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/accounts": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all accounts (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "List accounts", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 25, "description": "Items per page (default: 25, max: 100)", "name": "per_page", "in": "query"}], "responses": {"200": {"description": "List of accounts retrieved successfully", "schema": {"$ref": "#/definitions/handlers.AccountListResponse"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"description": "Create a new account for license management", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Create a new account", "parameters": [{"description": "Account creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AccountCreateRequest"}}], "responses": {"201": {"description": "Account created successfully", "schema": {"$ref": "#/definitions/handlers.AccountResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/accounts/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Get account by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Account retrieved successfully", "schema": {"$ref": "#/definitions/handlers.AccountResponse"}}, "400": {"description": "Invalid account ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing account's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Update account", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Account ID", "name": "id", "in": "path", "required": true}, {"description": "Account update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AccountUpdateRequest"}}], "responses": {"200": {"description": "Account updated successfully", "schema": {"$ref": "#/definitions/handlers.AccountResponse"}}, "400": {"description": "Invalid request data or account ID", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Account not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of licenses for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "List licenses", "parameters": [{"type": "integer", "example": 1, "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "example": 20, "description": "Page size (default: 20, max: 100)", "name": "page_size", "in": "query"}, {"type": "string", "example": "\"created_at\"", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "example": "\"DESC\"", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "List of licenses retrieved successfully", "schema": {"$ref": "#/definitions/handlers.LicenseListResponse"}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new license for a product with specified policy", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Create a new license", "parameters": [{"description": "License creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.LicenseCreateRequest"}}], "responses": {"201": {"description": "License created successfully", "schema": {"$ref": "#/definitions/handlers.LicenseResponse"}}, "400": {"description": "Invalid request data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/info": {"get": {"description": "Retrieves detailed information about a license without performing validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Get license information", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to get info for", "name": "license_key", "in": "query", "required": true}], "responses": {"200": {"description": "License information retrieved successfully", "schema": {"$ref": "#/definitions/handlers.LicenseInfoResponse"}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/quick-validate": {"get": {"description": "Performs a quick validation of a license key without detailed tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Quick license validation", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to validate", "name": "license_key", "in": "query", "required": true}], "responses": {"200": {"description": "License is valid", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Missing license key parameter", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "License is invalid", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/validate": {"post": {"description": "Validates a license key and returns detailed validation result including machine tracking", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate license", "parameters": [{"description": "License validation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ValidateLicenseRequest"}}], "responses": {"200": {"description": "License validation successful", "schema": {"$ref": "#/definitions/handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid request format", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}}}}, "/licenses/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific license", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Get license by ID", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "License retrieved successfully", "schema": {"$ref": "#/definitions/handlers.LicenseResponse"}}, "400": {"description": "Invalid license ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing license's properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Update license", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}, {"description": "License update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.LicenseUpdateRequest"}}], "responses": {"200": {"description": "License updated successfully", "schema": {"$ref": "#/definitions/handlers.LicenseResponse"}}, "400": {"description": "Invalid request data or license ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "License not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a license by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Delete license", "parameters": [{"type": "string", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "License ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "License deleted successfully"}, "400": {"description": "Invalid license ID format", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}, "401": {"description": "Authentication required", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}, "404": {"description": "License not found", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/responses.ErrorResponse"}}}}}}, "definitions": {"handlers.AccountCreateRequest": {"description": "Request payload for creating a new account", "type": "object", "required": ["email", "name", "slug"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Acme Corporation"}, "slug": {"type": "string", "maxLength": 255, "minLength": 1, "example": "acme-corp"}}}, "handlers.AccountListResponse": {"description": "Paginated list of accounts", "type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/definitions/handlers.AccountResponse"}}, "pagination": {"$ref": "#/definitions/handlers.PaginationInfo"}}}, "handlers.AccountResponse": {"description": "Account information returned by the API", "type": "object", "properties": {"created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "email": {"type": "string", "example": "<EMAIL>"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Acme Corporation"}, "slug": {"type": "string", "example": "acme-corp"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}}}, "handlers.AccountUpdateRequest": {"description": "Request payload for updating an account", "type": "object", "properties": {"metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Acme Corporation"}, "slug": {"type": "string", "maxLength": 255, "minLength": 1, "example": "updated-acme"}}}, "handlers.LicenseCreateRequest": {"description": "Request payload for creating a new license", "type": "object", "required": ["name", "policy_id"], "properties": {"expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Enterprise License"}, "policy_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "user_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}}}, "handlers.LicenseInfoResponse": {"description": "License information returned by info endpoints", "type": "object", "properties": {"account_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "claims": {"type": "object", "additionalProperties": true}, "expired": {"type": "boolean", "example": false}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "format": {"type": "string", "example": "json"}, "product_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "ACTIVE"}, "suspended": {"type": "boolean", "example": false}}}, "handlers.LicenseListResponse": {"description": "Paginated list of licenses", "type": "object", "properties": {"licenses": {"type": "array", "items": {"$ref": "#/definitions/handlers.LicenseResponse"}}, "pagination": {"$ref": "#/definitions/handlers.PaginationInfo"}}}, "handlers.LicenseResponse": {"description": "License information returned by the API", "type": "object", "properties": {"created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "example": "Enterprise License"}, "policy_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "ACTIVE"}, "updated_at": {"type": "string", "example": "2025-01-15T10:30:00Z"}, "user_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440002"}}}, "handlers.LicenseUpdateRequest": {"description": "Request payload for updating a license", "type": "object", "properties": {"expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "metadata": {"type": "object", "additionalProperties": true}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "Updated Enterprise License"}, "status": {"type": "string", "enum": ["ACTIVE", "SUSPENDED", "EXPIRED"], "example": "ACTIVE"}}}, "handlers.PaginationInfo": {"description": "Pagination information for list responses", "type": "object", "properties": {"page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 150}, "total_pages": {"type": "integer", "example": 8}}}, "handlers.ValidateLicenseRequest": {"description": "Request payload for validating a license", "type": "object", "required": ["license_key"], "properties": {"environment": {"type": "string", "example": "production"}, "license_key": {"type": "string", "example": "LIC-12345-ABCDE-67890-FGHIJ"}, "machine_fingerprint": {"type": "string", "example": "fp-mac-********"}, "machine_info": {"type": "object", "additionalProperties": true}}}, "handlers.ValidateLicenseResponse": {"description": "Response payload for license validation", "type": "object", "properties": {"account": {}, "cache_hit": {"type": "boolean", "example": false}, "claims": {"type": "object", "additionalProperties": true}, "errors": {"type": "array", "items": {"type": "string"}}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "license": {}, "machines_allowed": {"type": "integer", "example": 5}, "machines_used": {"type": "integer", "example": 2}, "policy": {}, "valid": {"type": "boolean", "example": true}, "validation_time": {"type": "string", "example": "2025-07-12T16:15:30Z"}, "warnings": {"type": "array", "items": {"type": "string"}}}}, "responses.ErrorCode": {"type": "string", "enum": ["UNAUTHORIZED", "INVALID_TOKEN", "TOKEN_EXPIRED", "INSUFFICIENT_SCOPE", "FORBIDDEN", "INSUFFICIENT_PERMISSIONS", "VALIDATION_FAILED", "INVALID_REQUEST", "MISSING_PARAMETER", "INVALID_PARAMETER", "NOT_FOUND", "RESOURCE_NOT_FOUND", "CONFLICT", "RESOURCE_EXISTS", "LICENSE_EXPIRED", "LICENSE_SUSPENDED", "LICENSE_INVALID", "LICENSE_NOT_FOUND", "MACHINE_HEARTBEAT_DEAD", "TOO_MANY_MACHINES", "NO_MACHINE", "CHECKOUT_ALGORITHM_INVALID", "CHECKOUT_INCLUDE_INVALID", "CHECKOUT_TTL_INVALID", "INTERNAL_ERROR", "SERVICE_UNAVAILABLE", "RATE_LIMIT_EXCEEDED"], "x-enum-varnames": ["ErrorCodeUnauthorized", "ErrorCodeInvalidToken", "ErrorCodeTokenExpired", "ErrorCodeInsufficientScope", "ErrorCodeForbidden", "ErrorCodeInsufficientPermissions", "ErrorCodeValidationFailed", "ErrorCodeInvalidRequest", "ErrorCodeMissingParameter", "ErrorCodeInvalidParameter", "ErrorCodeNotFound", "ErrorCodeResourceNotFound", "ErrorCodeConflict", "ErrorCodeResourceExists", "ErrorCodeLicenseExpired", "ErrorCodeLicenseSuspended", "ErrorCodeLicenseInvalid", "ErrorCodeLicenseNotFound", "ErrorCodeMachineHeartbeatDead", "ErrorCodeTooManyMachines", "ErrorCodeNoMachine", "ErrorCodeCheckoutAlgorithmInvalid", "ErrorCodeCheckoutIncludeInvalid", "ErrorCodeCheckoutTTLInvalid", "ErrorCodeInternalError", "ErrorCodeServiceUnavailable", "ErrorCodeRateLimitExceeded"]}, "responses.ErrorDetail": {"type": "object", "properties": {"code": {"$ref": "#/definitions/responses.ErrorCode"}, "detail": {"type": "string"}, "meta": {"type": "object", "additionalProperties": true}, "source": {"type": "object", "additionalProperties": true}, "title": {"type": "string"}}}, "responses.ErrorResponse": {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/responses.ErrorDetail"}}}}}, "securityDefinitions": {"ApiKeyAuth": {"description": "API key for authentication", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}, "BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "LicenseKeyAuth": {"description": "License key for validation endpoints", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-License-Key", "in": "header"}}, "tags": [{"description": "Authentication and authorization endpoints", "name": "Authentication"}, {"description": "License management and validation", "name": "Licenses"}, {"description": "Account management", "name": "Accounts"}, {"description": "Product management", "name": "Products"}, {"description": "Policy configuration", "name": "Policies"}, {"description": "Machine registration and tracking", "name": "Machines"}, {"description": "User management", "name": "Users"}, {"description": "System health and monitoring", "name": "Health"}, {"description": "System metrics and analytics", "name": "Metrics"}]}