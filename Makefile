# GoKeys Makefile

# Variables
APP_NAME := gokeys
BUILD_DIR := build
BINARY_NAME := $(APP_NAME)
DOCKER_IMAGE := $(APP_NAME)
DOCKER_TAG := latest

# Build information
VERSION ?= dev
COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
DATE := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
LDFLAGS := -X main.version=$(VERSION) -X main.commit=$(COMMIT) -X main.date=$(DATE)

# Go parameters
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# Default target
.PHONY: all
all: clean test build

# Test Dependencies Management
.PHONY: test-deps-up test-deps-down test-deps-logs test-deps-clean test-deps-status test-deps-admin
test-deps-up:
	@echo "🚀 Starting test dependencies (PostgreSQL + Valkey)..."
	docker-compose -f docker-compose.test.yml up -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 5
	@echo "✅ Test dependencies started!"
	@echo ""
	@echo "📊 Service URLs:"
	@echo "  PostgreSQL: localhost:5432"
	@echo "  Valkey:     localhost:6379"
	@echo "  pgAdmin:    http://localhost:8080 (run 'make test-deps-admin' to enable)"
	@echo ""
	@echo "🔗 Connection strings:"
	@echo "  DB: postgres://gokeys:gokeys_test_password@localhost:5432/gokeys_test"
	@echo "  Cache: valkey://localhost:6379"

test-deps-down:
	@echo "🛑 Stopping test dependencies..."
	docker-compose -f docker-compose.test.yml down
	@echo "✅ Test dependencies stopped!"

test-deps-logs:
	@echo "📋 Test dependencies logs:"
	docker-compose -f docker-compose.test.yml logs -f

test-deps-clean:
	@echo "🧹 Cleaning test dependencies and volumes..."
	docker-compose -f docker-compose.test.yml down -v --remove-orphans
	docker volume prune -f
	@echo "✅ Test dependencies cleaned!"

test-deps-status:
	@echo "📊 Test dependencies status:"
	docker-compose -f docker-compose.test.yml ps

test-deps-admin:
	@echo "🚀 Starting pgAdmin..."
	docker-compose -f docker-compose.test.yml --profile admin up -d pgadmin-test
	@echo "✅ pgAdmin available at: http://localhost:8080"
	@echo "   Email: <EMAIL>"
	@echo "   Password: admin123"

# Help target
.PHONY: help
help:
	@echo "GoKeys Makefile"
	@echo ""
	@echo "Available targets:"
	@echo ""
	@echo "Test Dependencies:"
	@echo "  test-deps-up       Start PostgreSQL and Valkey for testing"
	@echo "  test-deps-down     Stop test dependencies"
	@echo "  test-deps-logs     Show logs from test dependencies"
	@echo "  test-deps-clean    Remove test dependencies and volumes"
	@echo "  test-deps-status   Check status of test dependencies"
	@echo "  test-deps-admin    Start pgAdmin for database management"
	@echo ""
	@echo "Build:"
	@echo "  build              Build the application"
	@echo "  build-linux        Build for Linux"
	@echo "  build-windows      Build for Windows"
	@echo "  build-mac          Build for macOS"
	@echo "  test               Run tests"
	@echo "  test-coverage      Run tests with coverage"
	@echo "  test-integration   Run integration tests"
	@echo "  test-integration-verbose    Run integration tests (verbose)"
	@echo "  test-integration-coverage   Run integration tests with coverage"
	@echo "  test-integration-benchmark  Run integration benchmarks"
	@echo "  test-integration-all        Run complete integration test suite"
	@echo "  lint               Run linter"
	@echo "  security           Run security checks"
	@echo "  clean              Clean build artifacts"
	@echo "  deps               Download dependencies"
	@echo "  deps-update        Update dependencies"
	@echo "  docs               Generate API documentation"
	@echo "  docs-serve         Generate docs and start dev server"
	@echo "  docs-validate      Validate API documentation format"
	@echo ""
	@echo "Swagger:"
	@echo "  swagger-install    Install Swagger tools"
	@echo "  swagger-gen        Generate Swagger documentation"
	@echo "  swagger-serve      Generate docs and start server"
	@echo ""
	@echo "Quick Start:"
	@echo "  run                Build and run the server"
	@echo "  quick-run          Generate Swagger docs, build and run"
	@echo ""
	@echo "  docker-build       Build Docker image"
	@echo "  docker-run         Run Docker container"
	@echo "  deploy-dev         Deploy development environment"
	@echo "  deploy-prod        Deploy production environment"
	@echo "  help               Show this help message"

# Build targets
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -ldflags "$(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/server

.PHONY: build-linux
build-linux:
	@echo "Building for Linux..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -ldflags "-w -s $(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME)-linux ./cmd/server

.PHONY: build-windows
build-windows:
	@echo "Building for Windows..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) -ldflags "-w -s $(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME)-windows.exe ./cmd/server

.PHONY: build-mac
build-mac:
	@echo "Building for macOS..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) -ldflags "-w -s $(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME)-mac ./cmd/server

# Test targets
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v -race ./...

.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report: coverage.html"

.PHONY: test-integration
test-integration:
	@echo "Running entitlement integration tests..."
	./scripts/run-integration-tests.sh

.PHONY: test-integration-verbose
test-integration-verbose:
	@echo "Running entitlement integration tests (verbose)..."
	./scripts/run-integration-tests.sh -v

.PHONY: test-integration-coverage
test-integration-coverage:
	@echo "Running entitlement integration tests with coverage..."
	./scripts/run-integration-tests.sh -c -r

.PHONY: test-integration-benchmark
test-integration-benchmark:
	@echo "Running entitlement integration benchmarks..."
	./scripts/run-integration-tests.sh -b

.PHONY: test-integration-all
test-integration-all:
	@echo "Running complete entitlement integration test suite..."
	./scripts/run-integration-tests.sh -v -c -r -b

# New integration tests with Docker dependencies
.PHONY: test-handlers test-handlers-integration
test-handlers: test-deps-up
	@echo "🧪 Running handler tests with database..."
	@sleep 2  # Give services time to be ready
	$(GOTEST) ./internal/adapters/http/handlers/ -v
	@echo "✅ Handler tests completed!"

test-handlers-integration: test-deps-up
	@echo "🧪 Running handler integration tests..."
	@sleep 2
	$(GOTEST) ./internal/adapters/http/handlers/ -v -run Integration
	@echo "✅ Handler integration tests completed!"

.PHONY: test-with-deps
test-with-deps: test-deps-up
	@echo "🧪 Running all tests with database dependencies..."
	@sleep 2
	$(GOTEST) ./... -v
	@echo "✅ All tests with dependencies completed!"

.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Code quality targets
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run ./...

.PHONY: security
security:
	@echo "Running security checks..."
	gosec -fmt json -out gosec-report.json -stdout ./...

.PHONY: format
format:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

# Dependency targets
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

.PHONY: deps-update
deps-update:
	@echo "Updating dependencies..."
	$(GOMOD) get -u ./...
	$(GOMOD) tidy

# Clean target
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html
	rm -f gosec-report.json

# Docker targets
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 $(DOCKER_IMAGE):$(DOCKER_TAG)

# Deployment targets
.PHONY: deploy-dev
deploy-dev:
	@echo "Deploying development environment..."
	./scripts/deploy.sh development deploy

.PHONY: deploy-prod
deploy-prod:
	@echo "Deploying production environment..."
	./scripts/deploy.sh production deploy

.PHONY: stop-dev
stop-dev:
	@echo "Stopping development environment..."
	./scripts/deploy.sh development stop

.PHONY: stop-prod
stop-prod:
	@echo "Stopping production environment..."
	./scripts/deploy.sh production stop

# Database targets
.PHONY: migrate
migrate:
	@echo "Running database migrations..."
	$(GOBUILD) -o $(BUILD_DIR)/migrate ./cmd/migrate
	./$(BUILD_DIR)/migrate

.PHONY: migrate-down
migrate-down:
	@echo "Rolling back database migrations..."
	$(GOBUILD) -o $(BUILD_DIR)/migrate ./cmd/migrate
	./$(BUILD_DIR)/migrate down

# Development targets
.PHONY: dev
dev:
	@echo "Starting development server..."
	air -c .air.toml

.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/air-verse/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Documentation targets
.PHONY: docs
docs:
	@echo "Generating API documentation..."
	./scripts/generate-docs.sh

.PHONY: docs-serve
docs-serve: docs
	@echo "Starting development server with documentation..."
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/server
	APP_ENV=development ./$(BUILD_DIR)/$(BINARY_NAME)

.PHONY: docs-validate
docs-validate:
	@echo "Validating API documentation..."
	swag fmt -g cmd/server/main.go

# Swagger targets
.PHONY: swagger-install
swagger-install:
	@echo "Installing Swagger tools..."
	$(GOGET) github.com/swaggo/swag/cmd/swag@latest
	@echo "✅ Swagger tools installed!"

.PHONY: swagger-gen
swagger-gen:
	@echo "🔄 Generating Swagger documentation..."
	@if ! command -v swag >/dev/null 2>&1 && [ ! -f "$$HOME/go/bin/swag" ]; then \
		echo "⚠️  swag not found, installing..."; \
		$(GOGET) github.com/swaggo/swag/cmd/swag@latest; \
	fi
	@if command -v swag >/dev/null 2>&1; then \
		swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal; \
	elif [ -f "$$HOME/go/bin/swag" ]; then \
		$$HOME/go/bin/swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal; \
	else \
		echo "❌ Failed to find swag command"; \
		exit 1; \
	fi
	@echo "✅ Swagger documentation generated!"

.PHONY: swagger-serve
swagger-serve: swagger-gen build
	@echo "🚀 Starting server with updated Swagger docs..."
	@echo "📖 Swagger UI will be available at: http://localhost:8080/swagger/index.html"
	./$(BUILD_DIR)/$(BINARY_NAME)

.PHONY: run
run: build
	@echo "🚀 Running GoKeys server..."
	@echo "📖 Swagger UI: http://localhost:8080/swagger/index.html"
	@echo "🔗 API Base URL: http://localhost:8080/api/v1"
	./$(BUILD_DIR)/$(BINARY_NAME)

.PHONY: quick-run
quick-run: swagger-gen build
	@echo "🚀 Quick run: Generating Swagger docs and starting server..."
	@echo "📖 Swagger UI: http://localhost:8080/swagger/index.html"
	@echo "🔗 API Base URL: http://localhost:8080/api/v1"
	./$(BUILD_DIR)/$(BINARY_NAME)

# Release targets
.PHONY: release
release: clean test lint security docs build-linux build-windows build-mac
	@echo "Release built successfully!"
	@ls -la $(BUILD_DIR)/