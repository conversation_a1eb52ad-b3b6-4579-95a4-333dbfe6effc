package auth

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
)

// TokenType represents the type of token
type TokenType string

const (
	TokenTypeAdmin       TokenType = "admin"
	TokenTypeUser        TokenType = "user"
	TokenTypeEnvironment TokenType = "environment"
	TokenTypeProduct     TokenType = "product"
	TokenTypeLicense     TokenType = "license"
	TokenTypeDeveloper   TokenType = "developer"
	TokenTypeSalesAgent  TokenType = "sales"
	TokenTypeSupportAgent TokenType = "support"
	TokenTypeReadOnly    TokenType = "read"
)

// TokenVersion represents the token algorithm version
const (
	TokenVersionV3 = "v3"
	DefaultTokenVersion = TokenVersionV3
)

// Token represents an authentication token
type Token struct {
	ID          uuid.UUID    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Digest      string       `json:"-" gorm:"type:varchar(255);not null;unique;index"`
	BearerID    uuid.UUID    `json:"bearer_id" gorm:"type:uuid;not null;index"`
	BearerType  string       `json:"bearer_type" gorm:"type:varchar(50);not null"`
	AccountID   uuid.UUID    `json:"account_id" gorm:"type:uuid;not null;index"`
	Permissions []string     `json:"permissions" gorm:"type:text[]"`
	ExpiresAt   *time.Time   `json:"expires_at,omitempty"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	
	// Transient fields
	RawToken    string       `json:"raw_token,omitempty" gorm:"-"`
	TokenType   TokenType    `json:"token_type,omitempty" gorm:"-"`
}

// TokenDuration is the default duration for user tokens
const TokenDuration = 2 * 7 * 24 * time.Hour // 2 weeks

// GenerateToken creates a new token with the specified parameters
func GenerateToken(bearerID uuid.UUID, bearerType string, accountID uuid.UUID, secretKey string, tokenType TokenType) (*Token, error) {
	token := &Token{
		ID:         uuid.New(),
		BearerID:   bearerID,
		BearerType: bearerType,
		AccountID:  accountID,
		TokenType:  tokenType,
	}

	// Set default permissions based on token type
	token.SetDefaultPermissions()

	// Set expiry for user tokens
	if tokenType == TokenTypeUser {
		expiresAt := time.Now().Add(TokenDuration)
		token.ExpiresAt = &expiresAt
	}

	// Generate the actual token
	if err := token.Generate(secretKey); err != nil {
		return nil, err
	}

	return token, nil
}

// Generate creates the raw token and digest
func (t *Token) Generate(secretKey string) error {
	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return fmt.Errorf("failed to generate random token: %w", err)
	}

	// Create raw token with version
	rawToken := hex.EncodeToString(tokenBytes) + DefaultTokenVersion

	// Add prefix based on token type
	prefix := t.GetPrefix()
	if prefix != "" {
		rawToken = prefix + "-" + rawToken
	}

	// Create HMAC digest
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawToken))
	digest := hex.EncodeToString(h.Sum(nil))

	t.RawToken = rawToken
	t.Digest = digest

	return nil
}

// VerifyToken verifies a raw token against the stored digest
func (t *Token) VerifyToken(rawToken, secretKey string) bool {
	if rawToken == "" || t.Digest == "" {
		return false
	}

	// Create HMAC digest of the provided token
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawToken))
	expectedDigest := hex.EncodeToString(h.Sum(nil))

	// Constant-time comparison
	return hmac.Equal([]byte(t.Digest), []byte(expectedDigest))
}

// IsExpired checks if the token is expired
func (t *Token) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return t.ExpiresAt.Before(time.Now())
}

// GetPrefix returns the token prefix based on type
func (t *Token) GetPrefix() string {
	switch t.TokenType {
	case TokenTypeAdmin:
		return "admin"
	case TokenTypeUser:
		return "user"
	case TokenTypeEnvironment:
		return "env"
	case TokenTypeProduct:
		return "prod"
	case TokenTypeLicense:
		return "activ"
	case TokenTypeDeveloper:
		return "dev"
	case TokenTypeSalesAgent:
		return "sales"
	case TokenTypeSupportAgent:
		return "spprt"
	case TokenTypeReadOnly:
		return "read"
	default:
		return "token"
	}
}

// GetKind returns a human-readable token kind
func (t *Token) GetKind() string {
	switch t.TokenType {
	case TokenTypeAdmin:
		return "admin-token"
	case TokenTypeUser:
		return "user-token"
	case TokenTypeEnvironment:
		return "environment-token"
	case TokenTypeProduct:
		return "product-token"
	case TokenTypeLicense:
		return "activation-token"
	case TokenTypeDeveloper:
		return "developer-token"
	case TokenTypeSalesAgent:
		return "sales-token"
	case TokenTypeSupportAgent:
		return "support-token"
	case TokenTypeReadOnly:
		return "read-only-token"
	default:
		return "token"
	}
}

// SetDefaultPermissions sets the default permissions based on token type
func (t *Token) SetDefaultPermissions() {
	switch t.TokenType {
	case TokenTypeAdmin:
		t.Permissions = []string{WildcardPermission}
	case TokenTypeUser:
		t.Permissions = UserPermissions
	case TokenTypeEnvironment:
		t.Permissions = EnvironmentPermissions
	case TokenTypeProduct:
		t.Permissions = ProductPermissions
	case TokenTypeLicense:
		t.Permissions = LicensePermissions
	case TokenTypeDeveloper:
		t.Permissions = []string{WildcardPermission}
	case TokenTypeSalesAgent:
		t.Permissions = SalesAgentPermissions
	case TokenTypeSupportAgent:
		t.Permissions = SupportAgentPermissions
	case TokenTypeReadOnly:
		t.Permissions = ReadOnlyPermissions
	default:
		t.Permissions = []string{}
	}
}

// HasPermission checks if the token has a specific permission
func (t *Token) HasPermission(permission string) bool {
	return HasPermission(t.Permissions, permission)
}

// HasAnyPermission checks if the token has any of the specified permissions
func (t *Token) HasAnyPermission(permissions ...string) bool {
	return HasAnyPermission(t.Permissions, permissions...)
}

// HasAllPermissions checks if the token has all of the specified permissions
func (t *Token) HasAllPermissions(permissions ...string) bool {
	return HasAllPermissions(t.Permissions, permissions...)
}

// CanPerform is an alias for HasPermission for better readability
func (t *Token) CanPerform(action string) bool {
	return t.HasPermission(action)
}

// IsWildcard checks if the token has wildcard permissions
func (t *Token) IsWildcard() bool {
	return HasPermission(t.Permissions, WildcardPermission)
}

// DetermineTokenType determines the token type from the raw token prefix
func DetermineTokenType(rawToken string) TokenType {
	if strings.HasPrefix(rawToken, "admin-") {
		return TokenTypeAdmin
	}
	if strings.HasPrefix(rawToken, "user-") {
		return TokenTypeUser
	}
	if strings.HasPrefix(rawToken, "env-") {
		return TokenTypeEnvironment
	}
	if strings.HasPrefix(rawToken, "prod-") {
		return TokenTypeProduct
	}
	if strings.HasPrefix(rawToken, "activ-") {
		return TokenTypeLicense
	}
	if strings.HasPrefix(rawToken, "dev-") {
		return TokenTypeDeveloper
	}
	if strings.HasPrefix(rawToken, "sales-") {
		return TokenTypeSalesAgent
	}
	if strings.HasPrefix(rawToken, "spprt-") {
		return TokenTypeSupportAgent
	}
	if strings.HasPrefix(rawToken, "read-") {
		return TokenTypeReadOnly
	}
	
	// Default to user token for backward compatibility
	return TokenTypeUser
}

// ValidatePermissions validates that all permissions are valid
func (t *Token) ValidatePermissions() error {
	return ValidatePermissions(t.Permissions)
}

// AddPermission adds a permission to the token
func (t *Token) AddPermission(permission string) error {
	if t.HasPermission(permission) {
		return nil // Already has permission
	}
	
	if err := ValidatePermissions([]string{permission}); err != nil {
		return err
	}
	
	t.Permissions = append(t.Permissions, permission)
	return nil
}

// RemovePermission removes a permission from the token
func (t *Token) RemovePermission(permission string) {
	var filtered []string
	for _, perm := range t.Permissions {
		if perm != permission {
			filtered = append(filtered, perm)
		}
	}
	t.Permissions = filtered
}

// Regenerate creates a new token digest while keeping the same permissions
func (t *Token) Regenerate(secretKey string) error {
	// Extend expiry for user tokens
	if t.TokenType == TokenTypeUser && t.ExpiresAt != nil {
		expiresAt := time.Now().Add(TokenDuration)
		t.ExpiresAt = &expiresAt
	}
	
	return t.Generate(secretKey)
}

// TableName returns the table name for GORM
func (Token) TableName() string {
	return "tokens"
}

// BeforeCreate sets default permissions if none are specified
func (t *Token) BeforeCreate() error {
	if len(t.Permissions) == 0 {
		t.SetDefaultPermissions()
	}
	return t.ValidatePermissions()
}

// BeforeUpdate validates permissions before updating
func (t *Token) BeforeUpdate() error {
	return t.ValidatePermissions()
}
