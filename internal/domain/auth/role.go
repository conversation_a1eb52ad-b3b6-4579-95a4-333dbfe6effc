package auth

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// RoleType represents the type of role
type RoleType string

const (
	RoleTypeUser        RoleType = "user"
	RoleTypeAdmin       RoleType = "admin"
	RoleTypeDeveloper   RoleType = "developer"
	RoleTypeReadOnly    RoleType = "read_only"
	RoleTypeSalesAgent  RoleType = "sales_agent"
	RoleTypeSupportAgent RoleType = "support_agent"
	RoleTypeEnvironment RoleType = "environment"
	RoleTypeProduct     RoleType = "product"
	RoleTypeLicense     RoleType = "license"
)

// Role represents a role in the system with associated permissions
type Role struct {
	ID          uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        RoleType    `json:"name" gorm:"type:varchar(50);not null"`
	ResourceID  *uuid.UUID  `json:"resource_id,omitempty" gorm:"type:uuid"`
	ResourceType *string    `json:"resource_type,omitempty" gorm:"type:varchar(50)"`
	AccountID   uuid.UUID   `json:"account_id" gorm:"type:uuid;not null;index"`
	Permissions []string    `json:"permissions" gorm:"type:text[]"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// RoleRank defines the hierarchy of roles (higher number = more privileges)
var RoleRank = map[RoleType]int{
	RoleTypeAdmin:        7,
	RoleTypeEnvironment:  6,
	RoleTypeDeveloper:    5,
	RoleTypeProduct:      4,
	RoleTypeSalesAgent:   3,
	RoleTypeSupportAgent: 2,
	RoleTypeReadOnly:     1,
	RoleTypeLicense:      0,
	RoleTypeUser:         0,
}

// UserRoles defines valid roles for users
var UserRoles = []RoleType{
	RoleTypeUser,
	RoleTypeAdmin,
	RoleTypeDeveloper,
	RoleTypeReadOnly,
	RoleTypeSalesAgent,
	RoleTypeSupportAgent,
}

// EnvironmentRoles defines valid roles for environments
var EnvironmentRoles = []RoleType{
	RoleTypeEnvironment,
}

// ProductRoles defines valid roles for products
var ProductRoles = []RoleType{
	RoleTypeProduct,
}

// LicenseRoles defines valid roles for licenses
var LicenseRoles = []RoleType{
	RoleTypeLicense,
}

// GetDefaultPermissions returns the default permissions for a role type
func (r *Role) GetDefaultPermissions() []string {
	switch r.Name {
	case RoleTypeAdmin:
		return AllPermissions
	case RoleTypeEnvironment:
		return EnvironmentPermissions
	case RoleTypeDeveloper:
		return AllPermissions // Developers have full access like admins
	case RoleTypeProduct:
		return ProductPermissions
	case RoleTypeSalesAgent:
		return SalesAgentPermissions
	case RoleTypeSupportAgent:
		return SupportAgentPermissions
	case RoleTypeReadOnly:
		return ReadOnlyPermissions
	case RoleTypeUser:
		return UserPermissions
	case RoleTypeLicense:
		return LicensePermissions
	default:
		return []string{}
	}
}

// GetAllowedPermissions returns all permissions that this role type can have
func (r *Role) GetAllowedPermissions() []string {
	// All roles can have wildcard permission
	allowed := append(r.GetDefaultPermissions(), WildcardPermission)
	return allowed
}

// HasPermission checks if the role has a specific permission
func (r *Role) HasPermission(permission string) bool {
	return HasPermission(r.Permissions, permission)
}

// HasAnyPermission checks if the role has any of the specified permissions
func (r *Role) HasAnyPermission(permissions ...string) bool {
	return HasAnyPermission(r.Permissions, permissions...)
}

// HasAllPermissions checks if the role has all of the specified permissions
func (r *Role) HasAllPermissions(permissions ...string) bool {
	return HasAllPermissions(r.Permissions, permissions...)
}

// CanPerform is an alias for HasPermission for better readability
func (r *Role) CanPerform(action string) bool {
	return r.HasPermission(action)
}

// IsWildcard checks if the role has wildcard permissions
func (r *Role) IsWildcard() bool {
	return HasPermission(r.Permissions, WildcardPermission)
}

// GetRank returns the rank of this role
func (r *Role) GetRank() int {
	if rank, exists := RoleRank[r.Name]; exists {
		return rank
	}
	return -1
}

// IsHigherRankThan checks if this role has higher rank than another
func (r *Role) IsHigherRankThan(other *Role) bool {
	return r.GetRank() > other.GetRank()
}

// IsLowerRankThan checks if this role has lower rank than another
func (r *Role) IsLowerRankThan(other *Role) bool {
	return r.GetRank() < other.GetRank()
}

// IsEqualRankTo checks if this role has equal rank to another
func (r *Role) IsEqualRankTo(other *Role) bool {
	return r.GetRank() == other.GetRank()
}

// ValidatePermissions validates that all permissions are allowed for this role
func (r *Role) ValidatePermissions() error {
	allowed := r.GetAllowedPermissions()
	for _, perm := range r.Permissions {
		if !contains(allowed, perm) {
			return fmt.Errorf("permission %s is not allowed for role %s", perm, r.Name)
		}
	}
	return nil
}

// SetDefaultPermissions sets the role's permissions to the default set
func (r *Role) SetDefaultPermissions() {
	r.Permissions = r.GetDefaultPermissions()
}

// AddPermission adds a permission to the role if it's allowed
func (r *Role) AddPermission(permission string) error {
	if r.HasPermission(permission) {
		return nil // Already has permission
	}
	
	allowed := r.GetAllowedPermissions()
	if !contains(allowed, permission) {
		return fmt.Errorf("permission %s is not allowed for role %s", permission, r.Name)
	}
	
	r.Permissions = append(r.Permissions, permission)
	return nil
}

// RemovePermission removes a permission from the role
func (r *Role) RemovePermission(permission string) {
	var filtered []string
	for _, perm := range r.Permissions {
		if perm != permission {
			filtered = append(filtered, perm)
		}
	}
	r.Permissions = filtered
}

// IsUserRole checks if this is a user role
func (r *Role) IsUserRole() bool {
	for _, userRole := range UserRoles {
		if r.Name == userRole {
			return true
		}
	}
	return false
}

// IsEnvironmentRole checks if this is an environment role
func (r *Role) IsEnvironmentRole() bool {
	for _, envRole := range EnvironmentRoles {
		if r.Name == envRole {
			return true
		}
	}
	return false
}

// IsProductRole checks if this is a product role
func (r *Role) IsProductRole() bool {
	for _, prodRole := range ProductRoles {
		if r.Name == prodRole {
			return true
		}
	}
	return false
}

// IsLicenseRole checks if this is a license role
func (r *Role) IsLicenseRole() bool {
	for _, licRole := range LicenseRoles {
		if r.Name == licRole {
			return true
		}
	}
	return false
}

// ValidateRoleForResource validates that the role is appropriate for the resource type
func (r *Role) ValidateRoleForResource() error {
	if r.ResourceType == nil {
		return nil // No resource type specified
	}

	switch *r.ResourceType {
	case "User":
		if !r.IsUserRole() {
			return fmt.Errorf("role %s is not valid for User resource", r.Name)
		}
	case "Environment":
		if !r.IsEnvironmentRole() {
			return fmt.Errorf("role %s is not valid for Environment resource", r.Name)
		}
	case "Product":
		if !r.IsProductRole() {
			return fmt.Errorf("role %s is not valid for Product resource", r.Name)
		}
	case "License":
		if !r.IsLicenseRole() {
			return fmt.Errorf("role %s is not valid for License resource", r.Name)
		}
	default:
		return fmt.Errorf("unknown resource type: %s", *r.ResourceType)
	}

	return nil
}

// TableName returns the table name for GORM
func (Role) TableName() string {
	return "roles"
}

// BeforeCreate sets default permissions if none are specified
func (r *Role) BeforeCreate() error {
	if len(r.Permissions) == 0 {
		r.SetDefaultPermissions()
	}
	return r.ValidatePermissions()
}

// BeforeUpdate validates permissions before updating
func (r *Role) BeforeUpdate() error {
	return r.ValidatePermissions()
}
