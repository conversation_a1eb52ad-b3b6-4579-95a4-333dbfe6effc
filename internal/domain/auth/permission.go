package auth

import (
	"fmt"
	"strings"
)

// Permission represents a specific action that can be performed in the system
type Permission struct {
	ID     string `json:"id"`
	Action string `json:"action"`
}

// WildcardPermission is the special permission that grants all access
const WildcardPermission = "*"

// AllPermissions defines all available permissions in the system
// Based on Ruby Permission::ALL_PERMISSIONS
var AllPermissions = []string{
	// Account permissions
	"account.analytics.read",
	"account.billing.read",
	"account.billing.update",
	"account.plan.read",
	"account.plan.update",
	"account.read",
	"account.subscription.read",
	"account.subscription.update",
	"account.update",

	// Admin permissions
	"admin.create",
	"admin.delete",
	"admin.invite",
	"admin.read",
	"admin.update",

	// Architecture permissions
	"arch.read",

	// Artifact permissions
	"artifact.create",
	"artifact.delete",
	"artifact.read",
	"artifact.update",

	// Component permissions
	"component.create",
	"component.delete",
	"component.read",
	"component.update",

	// Channel permissions
	"channel.read",

	// Constraint permissions
	"constraint.read",

	// Engine permissions
	"engine.read",

	// Entitlement permissions
	"entitlement.create",
	"entitlement.delete",
	"entitlement.read",
	"entitlement.update",

	// Environment permissions
	"environment.create",
	"environment.delete",
	"environment.read",
	"environment.tokens.generate",
	"environment.update",

	// Event log permissions
	"event-log.read",

	// Group permissions
	"group.create",
	"group.delete",
	"group.licenses.read",
	"group.machines.read",
	"group.owners.attach",
	"group.owners.detach",
	"group.owners.read",
	"group.read",
	"group.update",
	"group.users.read",

	// Key permissions
	"key.create",
	"key.delete",
	"key.read",
	"key.update",

	// License permissions
	"license.check-in",
	"license.check-out",
	"license.create",
	"license.delete",
	"license.entitlements.attach",
	"license.entitlements.detach",
	"license.group.update",
	"license.owner.update",
	"license.policy.update",
	"license.read",
	"license.reinstate",
	"license.renew",
	"license.revoke",
	"license.suspend",
	"license.tokens.generate",
	"license.update",
	"license.usage.decrement",
	"license.usage.increment",
	"license.usage.reset",
	"license.user.update",
	"license.users.attach",
	"license.users.detach",
	"license.validate",

	// Machine permissions
	"machine.check-out",
	"machine.create",
	"machine.delete",
	"machine.group.update",
	"machine.heartbeat.ping",
	"machine.heartbeat.reset",
	"machine.owner.update",
	"machine.proofs.generate",
	"machine.read",
	"machine.update",

	// Metric permissions
	"metric.read",

	// Package permissions
	"package.create",
	"package.delete",
	"package.read",
	"package.update",

	// Platform permissions
	"platform.read",

	// Policy permissions
	"policy.create",
	"policy.delete",
	"policy.entitlements.attach",
	"policy.entitlements.detach",
	"policy.pool.pop",
	"policy.read",
	"policy.update",

	// Process permissions
	"process.create",
	"process.delete",
	"process.heartbeat.ping",
	"process.read",
	"process.update",

	// Product permissions
	"product.create",
	"product.delete",
	"product.read",
	"product.tokens.generate",
	"product.update",

	// Release permissions
	"release.constraints.attach",
	"release.constraints.detach",
	"release.create",
	"release.delete",
	"release.download",
	"release.package.update",
	"release.publish",
	"release.read",
	"release.update",
	"release.upgrade",
	"release.upload",
	"release.yank",

	// Request log permissions
	"request-log.read",

	// Token permissions
	"token.generate",
	"token.read",
	"token.regenerate",
	"token.revoke",

	// User permissions
	"user.ban",
	"user.create",
	"user.delete",
	"user.group.update",
	"user.invite",
	"user.password.reset",
	"user.password.update",
	"user.read",
	"user.second-factors.create",
	"user.second-factors.delete",
	"user.second-factors.read",
	"user.second-factors.update",
	"user.tokens.generate",
	"user.unban",
	"user.update",

	// Webhook permissions
	"webhook-endpoint.create",
	"webhook-endpoint.delete",
	"webhook-endpoint.read",
	"webhook-endpoint.update",
	"webhook-event.delete",
	"webhook-event.read",
	"webhook-event.retry",
}

// EnvironmentPermissions defines permissions available for environment tokens
// Based on Ruby Permission::ENVIRONMENT_PERMISSIONS
var EnvironmentPermissions = []string{
	"account.read",
	"arch.read",
	"artifact.create", "artifact.delete", "artifact.read", "artifact.update",
	"component.create", "component.delete", "component.read", "component.update",
	"channel.read",
	"constraint.read",
	"engine.read",
	"entitlement.create", "entitlement.delete", "entitlement.read", "entitlement.update",
	"environment.read",
	"event-log.read",
	"group.create", "group.delete", "group.licenses.read", "group.machines.read",
	"group.owners.attach", "group.owners.detach", "group.owners.read", "group.read", "group.update", "group.users.read",
	"key.create", "key.delete", "key.read", "key.update",
	"license.check-in", "license.check-out", "license.create", "license.delete",
	"license.entitlements.attach", "license.entitlements.detach", "license.group.update",
	"license.owner.update", "license.policy.update", "license.read", "license.reinstate",
	"license.renew", "license.revoke", "license.suspend", "license.tokens.generate",
	"license.update", "license.usage.decrement", "license.usage.increment", "license.usage.reset",
	"license.user.update", "license.users.attach", "license.users.detach", "license.validate",
	"machine.check-out", "machine.create", "machine.delete", "machine.group.update",
	"machine.heartbeat.ping", "machine.heartbeat.reset", "machine.owner.update",
	"machine.proofs.generate", "machine.read", "machine.update",
	"package.create", "package.delete", "package.read", "package.update",
	"platform.read",
	"policy.create", "policy.delete", "policy.entitlements.attach", "policy.entitlements.detach",
	"policy.pool.pop", "policy.read", "policy.update",
	"process.create", "process.delete", "process.heartbeat.ping", "process.read", "process.update",
	"product.create", "product.delete", "product.read", "product.tokens.generate", "product.update",
	"release.constraints.attach", "release.constraints.detach", "release.create", "release.delete",
	"release.download", "release.package.update", "release.publish", "release.read",
	"release.update", "release.upgrade", "release.upload", "release.yank",
	"request-log.read",
	"token.generate", "token.read", "token.regenerate", "token.revoke",
	"user.ban", "user.create", "user.delete", "user.group.update", "user.invite",
	"user.password.reset", "user.password.update", "user.read",
	"user.second-factors.create", "user.second-factors.delete", "user.second-factors.read", "user.second-factors.update",
	"user.tokens.generate", "user.unban", "user.update",
	"webhook-endpoint.create", "webhook-endpoint.delete", "webhook-endpoint.read", "webhook-endpoint.update",
	"webhook-event.delete", "webhook-event.read", "webhook-event.retry",
}

// ReadOnlyPermissions defines permissions for read-only access
// Based on Ruby Permission::READ_ONLY_PERMISSIONS
var ReadOnlyPermissions = []string{
	"account.analytics.read", "account.billing.read", "account.plan.read", "account.read", "account.subscription.read",
	"admin.read",
	"arch.read",
	"artifact.read",
	"component.read",
	"channel.read",
	"constraint.read",
	"engine.read",
	"entitlement.read",
	"environment.read",
	"event-log.read",
	"group.licenses.read", "group.machines.read", "group.owners.read", "group.read", "group.users.read",
	"key.read",
	"license.read", "license.validate",
	"machine.read",
	"metric.read",
	"package.read",
	"platform.read",
	"policy.read",
	"process.read",
	"product.read",
	"release.download", "release.read", "release.upgrade",
	"request-log.read",
	"token.generate", "token.read",
	"user.password.reset", "user.password.update", "user.read", "user.second-factors.read",
	"webhook-endpoint.read", "webhook-event.read",
}

// ProductPermissions defines permissions available for product tokens
// Based on Ruby Permission::PRODUCT_PERMISSIONS
var ProductPermissions = []string{
	"account.read",
	"arch.read",
	"artifact.create", "artifact.delete", "artifact.read", "artifact.update",
	"component.create", "component.delete", "component.read", "component.update",
	"channel.read",
	"constraint.read",
	"engine.read",
	"entitlement.read",
	"group.create", "group.delete", "group.licenses.read", "group.machines.read",
	"group.owners.attach", "group.owners.detach", "group.owners.read", "group.read", "group.update", "group.users.read",
	"key.create", "key.delete", "key.read", "key.update",
	"license.check-in", "license.check-out", "license.create", "license.delete",
	"license.entitlements.attach", "license.entitlements.detach", "license.group.update",
	"license.owner.update", "license.policy.update", "license.read", "license.reinstate",
	"license.renew", "license.revoke", "license.suspend", "license.tokens.generate",
	"license.update", "license.usage.decrement", "license.usage.increment", "license.usage.reset",
	"license.user.update", "license.users.attach", "license.users.detach", "license.validate",
	"machine.check-out", "machine.create", "machine.delete", "machine.group.update",
	"machine.heartbeat.ping", "machine.heartbeat.reset", "machine.owner.update",
	"machine.proofs.generate", "machine.read", "machine.update",
	"package.create", "package.delete", "package.read", "package.update",
	"platform.read",
	"policy.create", "policy.delete", "policy.entitlements.attach", "policy.entitlements.detach",
	"policy.pool.pop", "policy.read", "policy.update",
	"process.create", "process.delete", "process.heartbeat.ping", "process.read", "process.update",
	"product.read", "product.update",
	"release.constraints.attach", "release.constraints.detach", "release.create", "release.delete",
	"release.download", "release.package.update", "release.publish", "release.read",
	"release.update", "release.upgrade", "release.upload", "release.yank",
	"token.generate", "token.read", "token.regenerate", "token.revoke",
	"user.ban", "user.create", "user.delete", "user.group.update", "user.read", "user.tokens.generate", "user.unban", "user.update",
	"webhook-endpoint.create", "webhook-endpoint.delete", "webhook-endpoint.read", "webhook-endpoint.update",
	"webhook-event.read",
}

// UserPermissions defines permissions available for user tokens
// Based on Ruby Permission::USER_PERMISSIONS
var UserPermissions = []string{
	"account.read",
	"arch.read",
	"artifact.read",
	"component.create", "component.delete", "component.read", "component.update",
	"channel.read",
	"constraint.read",
	"engine.read",
	"entitlement.read",
	"group.licenses.read", "group.machines.read", "group.owners.read", "group.read", "group.users.read",
	"license.check-in", "license.check-out", "license.create", "license.delete",
	"license.policy.update", "license.read", "license.renew", "license.revoke",
	"license.usage.increment", "license.validate", "license.users.attach", "license.users.detach",
	"machine.check-out", "machine.create", "machine.delete", "machine.heartbeat.ping",
	"machine.proofs.generate", "machine.read", "machine.update",
	"package.read",
	"platform.read",
	"policy.read",
	"process.create", "process.delete", "process.heartbeat.ping", "process.read", "process.update",
	"product.read",
	"release.download", "release.read", "release.upgrade",
	"token.generate", "token.read", "token.regenerate", "token.revoke",
	"user.password.reset", "user.password.update", "user.read",
	"user.second-factors.create", "user.second-factors.delete", "user.second-factors.read", "user.second-factors.update",
	"user.update",
}

// LicensePermissions defines permissions available for license tokens
// Based on Ruby Permission::LICENSE_PERMISSIONS
var LicensePermissions = []string{
	"account.read",
	"arch.read",
	"artifact.read",
	"component.create", "component.delete", "component.read", "component.update",
	"channel.read",
	"constraint.read",
	"engine.read",
	"entitlement.read",
	"group.owners.read", "group.read",
	"license.check-in", "license.check-out", "license.read", "license.usage.increment", "license.validate",
	"machine.check-out", "machine.create", "machine.delete", "machine.heartbeat.ping",
	"machine.proofs.generate", "machine.read", "machine.update",
	"package.read",
	"platform.read",
	"policy.read",
	"process.create", "process.delete", "process.heartbeat.ping", "process.read", "process.update",
	"product.read",
	"release.download", "release.read", "release.upgrade",
	"token.read", "token.regenerate", "token.revoke",
	"user.read",
}

// SalesAgentPermissions defines permissions for sales agents
var SalesAgentPermissions = []string{
	"account.read",
	"license.create", "license.read", "license.update",
	"user.create", "user.read", "user.update",
	"product.read",
	"policy.read",
	"group.read",
}

// SupportAgentPermissions defines permissions for support agents
var SupportAgentPermissions = []string{
	"account.read",
	"license.read", "license.validate",
	"user.read",
	"machine.read",
	"product.read",
	"policy.read",
	"group.read",
	"event-log.read",
	"request-log.read",
}

// HasPermission checks if a permission exists in a slice of permissions
func HasPermission(permissions []string, permission string) bool {
	// Check for wildcard permission
	for _, p := range permissions {
		if p == WildcardPermission {
			return true
		}
		if p == permission {
			return true
		}
	}
	return false
}

// HasAnyPermission checks if any of the required permissions exist
func HasAnyPermission(permissions []string, required ...string) bool {
	for _, req := range required {
		if HasPermission(permissions, req) {
			return true
		}
	}
	return false
}

// HasAllPermissions checks if all required permissions exist
func HasAllPermissions(permissions []string, required ...string) bool {
	for _, req := range required {
		if !HasPermission(permissions, req) {
			return false
		}
	}
	return true
}

// ValidatePermissions checks if all permissions are valid
func ValidatePermissions(permissions []string) error {
	for _, perm := range permissions {
		if perm == WildcardPermission {
			continue
		}
		if !contains(AllPermissions, perm) {
			return fmt.Errorf("invalid permission: %s", perm)
		}
	}
	return nil
}

// FilterPermissions returns only valid permissions from the input
func FilterPermissions(permissions []string, allowedPermissions []string) []string {
	var filtered []string
	for _, perm := range permissions {
		if perm == WildcardPermission || contains(allowedPermissions, perm) {
			filtered = append(filtered, perm)
		}
	}
	return filtered
}

// GetPermissionsByPrefix returns all permissions that start with the given prefix
func GetPermissionsByPrefix(prefix string) []string {
	var matched []string
	for _, perm := range AllPermissions {
		if strings.HasPrefix(perm, prefix) {
			matched = append(matched, perm)
		}
	}
	return matched
}

// contains checks if a string exists in a slice
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
