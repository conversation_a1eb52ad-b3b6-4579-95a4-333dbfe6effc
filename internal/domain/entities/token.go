package entities

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Token struct {
	ID            string          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string          `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string         `json:"environment_id" gorm:"type:uuid;index"`
	BearerID      string          `json:"bearer_id" gorm:"type:uuid;not null;index"`
	BearerType    TokenBearerType `json:"bearer_type" gorm:"not null"`
	Name          string          `json:"name"`

	// Token value (hashed in database)
	Digest string `json:"-" gorm:"unique;not null"` // SHA-256 hash of token
	Token  string `json:"token,omitempty" gorm:"-"` // Plain token (only in memory)

	// Token configuration
	Kind TokenKind `json:"kind" gorm:"default:bearer"`

	// Activation limits (for license tokens)
	MaxActivations   *int `json:"max_activations,omitempty"`
	MaxDeactivations *int `json:"max_deactivations,omitempty"`
	Activations      int  `json:"activations" gorm:"default:0"`
	Deactivations    int  `json:"deactivations" gorm:"default:0"`

	// Permissions (custom permissions for this token)
	CustomPermissions []string `json:"custom_permissions" gorm:"type:text[]"`

	// Expiration and usage
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	LastUsed  *time.Time `json:"last_used,omitempty"`
	RevokedAt *time.Time `json:"revoked_at,omitempty"`

	// Metadata for additional token information
	Metadata map[string]interface{} `json:"metadata,omitempty" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Sessions    []Session    `json:"sessions,omitempty"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:token_permissions"`
}

type TokenBearerType string

const (
	TokenBearerTypeUser        TokenBearerType = "user"
	TokenBearerTypeProduct     TokenBearerType = "product"
	TokenBearerTypeLicense     TokenBearerType = "license"
	TokenBearerTypeEnvironment TokenBearerType = "environment"
)

type TokenKind string

const (
	TokenKindBearer  TokenKind = "bearer"
	TokenKindSession TokenKind = "session"
)

// Token generation and validation methods

// GenerateToken creates a new token with the specified parameters
func (t *Token) GenerateToken(secretKey string) error {
	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return fmt.Errorf("failed to generate random token: %w", err)
	}

	// Create raw token with version
	rawToken := hex.EncodeToString(tokenBytes) + "v3"

	// Add prefix based on bearer type
	prefix := t.GetPrefix()
	if prefix != "" {
		rawToken = prefix + "-" + rawToken
	}

	// Create HMAC digest
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawToken))
	digest := hex.EncodeToString(h.Sum(nil))

	t.Token = rawToken
	t.Digest = digest

	return nil
}

// VerifyToken verifies a raw token against the stored digest
func (t *Token) VerifyToken(rawToken, secretKey string) bool {
	if rawToken == "" || t.Digest == "" {
		return false
	}

	// Create HMAC digest of the provided token
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawToken))
	expectedDigest := hex.EncodeToString(h.Sum(nil))

	// Constant-time comparison
	return hmac.Equal([]byte(t.Digest), []byte(expectedDigest))
}

// IsExpired checks if the token is expired
func (t *Token) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return t.ExpiresAt.Before(time.Now())
}

// IsRevoked checks if the token is revoked
func (t *Token) IsRevoked() bool {
	return t.RevokedAt != nil
}

// GetPrefix returns the token prefix based on bearer type
func (t *Token) GetPrefix() string {
	switch t.BearerType {
	case TokenBearerTypeUser:
		return "user"
	case TokenBearerTypeEnvironment:
		return "env"
	case TokenBearerTypeProduct:
		return "prod"
	case TokenBearerTypeLicense:
		return "activ"
	default:
		return "token"
	}
}

// GetTokenType returns the token type for compatibility with auth package
func (t *Token) GetTokenType() string {
	switch t.BearerType {
	case TokenBearerTypeUser:
		return "user"
	case TokenBearerTypeEnvironment:
		return "environment"
	case TokenBearerTypeProduct:
		return "product"
	case TokenBearerTypeLicense:
		return "license"
	default:
		return "token"
	}
}

// DetermineTokenType determines the token type from the raw token prefix
func DetermineTokenType(rawToken string) TokenBearerType {
	if strings.HasPrefix(rawToken, "user-") {
		return TokenBearerTypeUser
	}
	if strings.HasPrefix(rawToken, "env-") {
		return TokenBearerTypeEnvironment
	}
	if strings.HasPrefix(rawToken, "prod-") {
		return TokenBearerTypeProduct
	}
	if strings.HasPrefix(rawToken, "activ-") {
		return TokenBearerTypeLicense
	}

	// Default to user token for backward compatibility
	return TokenBearerTypeUser
}

// HasPermission checks if the token has a specific permission
func (t *Token) HasPermission(permission string) bool {
	// Check custom permissions first
	for _, perm := range t.CustomPermissions {
		if perm == "*" || perm == permission {
			return true
		}
	}

	// Check permissions from relationships
	for _, perm := range t.Permissions {
		if perm.Action == "*" || perm.Action == permission {
			return true
		}
	}

	return false
}

// HasAnyPermission checks if the token has any of the specified permissions
func (t *Token) HasAnyPermission(permissions ...string) bool {
	for _, perm := range permissions {
		if t.HasPermission(perm) {
			return true
		}
	}
	return false
}

// HasAllPermissions checks if the token has all of the specified permissions
func (t *Token) HasAllPermissions(permissions ...string) bool {
	for _, perm := range permissions {
		if !t.HasPermission(perm) {
			return false
		}
	}
	return true
}

// GetAccountUUID returns the account ID as UUID
func (t *Token) GetAccountUUID() (uuid.UUID, error) {
	return uuid.Parse(t.AccountID)
}

// GetBearerUUID returns the bearer ID as UUID
func (t *Token) GetBearerUUID() (uuid.UUID, error) {
	return uuid.Parse(t.BearerID)
}

// TableName returns the table name for GORM
func (Token) TableName() string {
	return "tokens"
}
