package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/auth"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// AuthService handles authentication and authorization operations
type AuthService struct {
	tokenRepo   repositories.TokenRepository
	userRepo    repositories.UserRepository
	licenseRepo repositories.LicenseRepository
	secretKey   string
}

// NewAuthService creates a new authentication service
func NewAuthService(
	tokenRepo repositories.TokenRepository,
	userRepo repositories.UserRepository,
	licenseRepo repositories.LicenseRepository,
	secretKey string,
) *AuthService {
	return &AuthService{
		tokenRepo:   tokenRepo,
		userRepo:    userRepo,
		licenseRepo: licenseRepo,
		secretKey:   secretKey,
	}
}

// AuthenticateToken authenticates a raw token and returns the token information
func (as *AuthService) AuthenticateToken(ctx context.Context, rawToken string) (*entities.Token, error) {
	if rawToken == "" {
		return nil, fmt.Errorf("empty token")
	}

	// Create HMAC digest of the raw token
	tempToken := &entities.Token{}
	tempToken.Token = rawToken
	if err := tempToken.GenerateToken(as.secretKey); err != nil {
		return nil, fmt.Errorf("failed to generate token digest: %w", err)
	}

	// Look up token by digest
	token, err := as.tokenRepo.GetByDigest(ctx, tempToken.Digest)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Verify the token
	if !token.VerifyToken(rawToken, as.secretKey) {
		return nil, fmt.Errorf("invalid token")
	}

	// Check if token is expired
	if token.IsExpired() {
		return nil, fmt.Errorf("token expired")
	}

	return token, nil
}

// AuthenticateLicenseKey authenticates a license key and returns token-like information
func (as *AuthService) AuthenticateLicenseKey(ctx context.Context, licenseKey string) (*entities.Token, error) {
	if licenseKey == "" {
		return nil, fmt.Errorf("empty license key")
	}

	// Look up license by key
	license, err := as.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return nil, fmt.Errorf("license not found: %w", err)
	}

	// Check if license is active
	if license.Status != entities.LicenseStatusActive {
		return nil, fmt.Errorf("license is not active")
	}

	// Check if license supports token authentication (this would be a policy setting)
	// For now, we'll assume all licenses support token auth

	// Create a token-like object for the license
	token := &entities.Token{
		ID:         uuid.New().String(),
		BearerID:   license.ID,
		BearerType: entities.TokenBearerTypeLicense,
		AccountID:  license.AccountID,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	return token, nil
}

// CreateToken creates a new token for a bearer
func (as *AuthService) CreateToken(ctx context.Context, bearerID uuid.UUID, bearerType entities.TokenBearerType, accountID uuid.UUID) (*entities.Token, error) {
	// Create the token
	token := &entities.Token{
		ID:         uuid.New().String(),
		BearerID:   bearerID.String(),
		BearerType: bearerType,
		AccountID:  accountID.String(),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// Generate the actual token
	if err := token.GenerateToken(as.secretKey); err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Save to database
	if err := as.tokenRepo.Create(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to save token: %w", err)
	}

	return token, nil
}

// RegenerateToken regenerates an existing token
func (as *AuthService) RegenerateToken(ctx context.Context, tokenID uuid.UUID) (*auth.Token, error) {
	// Get existing token
	token, err := as.tokenRepo.GetByID(ctx, tokenID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Regenerate the token
	if err := token.Regenerate(as.secretKey); err != nil {
		return nil, fmt.Errorf("failed to regenerate token: %w", err)
	}

	// Update in database
	if err := as.tokenRepo.Update(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	return token, nil
}

// RevokeToken revokes a token
func (as *AuthService) RevokeToken(ctx context.Context, tokenID uuid.UUID) error {
	return as.tokenRepo.Delete(ctx, tokenID)
}

// GetTokensByBearer gets all tokens for a bearer
func (as *AuthService) GetTokensByBearer(ctx context.Context, bearerID uuid.UUID, bearerType string) ([]*auth.Token, error) {
	return as.tokenRepo.GetByBearer(ctx, bearerID, bearerType)
}

// ValidatePermissions validates that a token has the required permissions
func (as *AuthService) ValidatePermissions(token *auth.Token, requiredPermissions ...string) error {
	if token == nil {
		return fmt.Errorf("token is nil")
	}

	if token.IsExpired() {
		return fmt.Errorf("token is expired")
	}

	if !token.HasAllPermissions(requiredPermissions...) {
		return fmt.Errorf("insufficient permissions")
	}

	return nil
}

// ValidateAnyPermission validates that a token has any of the required permissions
func (as *AuthService) ValidateAnyPermission(token *auth.Token, requiredPermissions ...string) error {
	if token == nil {
		return fmt.Errorf("token is nil")
	}

	if token.IsExpired() {
		return fmt.Errorf("token is expired")
	}

	if !token.HasAnyPermission(requiredPermissions...) {
		return fmt.Errorf("insufficient permissions")
	}

	return nil
}

// CreateUserToken creates a token for a user
func (as *AuthService) CreateUserToken(ctx context.Context, userID uuid.UUID, accountID uuid.UUID) (*auth.Token, error) {
	return as.CreateToken(ctx, userID, "User", accountID, auth.TokenTypeUser)
}

// CreateAdminToken creates a token for an admin user
func (as *AuthService) CreateAdminToken(ctx context.Context, userID uuid.UUID, accountID uuid.UUID) (*auth.Token, error) {
	return as.CreateToken(ctx, userID, "User", accountID, auth.TokenTypeAdmin)
}

// CreateEnvironmentToken creates a token for an environment
func (as *AuthService) CreateEnvironmentToken(ctx context.Context, environmentID uuid.UUID, accountID uuid.UUID) (*auth.Token, error) {
	return as.CreateToken(ctx, environmentID, "Environment", accountID, auth.TokenTypeEnvironment)
}

// CreateProductToken creates a token for a product
func (as *AuthService) CreateProductToken(ctx context.Context, productID uuid.UUID, accountID uuid.UUID) (*auth.Token, error) {
	return as.CreateToken(ctx, productID, "Product", accountID, auth.TokenTypeProduct)
}

// CreateLicenseToken creates a token for a license (activation token)
func (as *AuthService) CreateLicenseToken(ctx context.Context, licenseID uuid.UUID, accountID uuid.UUID) (*auth.Token, error) {
	return as.CreateToken(ctx, licenseID, "License", accountID, auth.TokenTypeLicense)
}

// GetTokenInfo returns detailed information about a token
func (as *AuthService) GetTokenInfo(ctx context.Context, tokenID uuid.UUID) (*auth.Token, error) {
	token, err := as.tokenRepo.GetByID(ctx, tokenID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Set token type for convenience
	if token.RawToken != "" {
		token.TokenType = auth.DetermineTokenType(token.RawToken)
	}

	return token, nil
}

// CleanupExpiredTokens removes expired tokens from the database
func (as *AuthService) CleanupExpiredTokens(ctx context.Context) error {
	return as.tokenRepo.DeleteExpired(ctx)
}

// GetActiveTokensCount returns the count of active tokens for a bearer
func (as *AuthService) GetActiveTokensCount(ctx context.Context, bearerID uuid.UUID, bearerType string) (int64, error) {
	tokens, err := as.tokenRepo.GetByBearer(ctx, bearerID, bearerType)
	if err != nil {
		return 0, err
	}

	count := int64(0)
	for _, token := range tokens {
		if !token.IsExpired() {
			count++
		}
	}

	return count, nil
}

// ExtendTokenExpiry extends the expiry time of a token
func (as *AuthService) ExtendTokenExpiry(ctx context.Context, tokenID uuid.UUID, duration time.Duration) error {
	token, err := as.tokenRepo.GetByID(ctx, tokenID)
	if err != nil {
		return fmt.Errorf("token not found: %w", err)
	}

	// Extend expiry
	if token.ExpiresAt != nil {
		newExpiry := token.ExpiresAt.Add(duration)
		token.ExpiresAt = &newExpiry
	} else {
		// If token doesn't expire, set expiry from now
		newExpiry := time.Now().Add(duration)
		token.ExpiresAt = &newExpiry
	}

	return as.tokenRepo.Update(ctx, token)
}
