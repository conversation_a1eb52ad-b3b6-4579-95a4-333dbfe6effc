package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type ProductHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewProductHandler(serviceCoordinator *services.ServiceCoordinator) *ProductHandler {
	return &ProductHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear

// ProductCreateRequest represents the request to create a new product
// @Description Request payload for creating a new product
type ProductCreateRequest struct {
	Name                 string                 `json:"name" binding:"required,min=1,max=255" example:"My Software Product"`
	Code                 string                 `json:"code" binding:"required,min=1,max=255,alphanum" example:"my-software"`
	Description          string                 `json:"description,omitempty" example:"A comprehensive software solution"`
	DistributionStrategy string                 `json:"distribution_strategy,omitempty" binding:"omitempty,oneof=licensed open" example:"licensed"`
	URL                  string                 `json:"url,omitempty" binding:"omitempty,url" example:"https://example.com/product"`
	Platforms            []string               `json:"platforms,omitempty" example:"windows,macos,linux"`
	EnvironmentID        *string                `json:"environment_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
}

// ProductUpdateRequest represents the request to update an existing product
// @Description Request payload for updating a product
type ProductUpdateRequest struct {
	Name                 *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Updated Software Product"`
	Code                 *string                `json:"code,omitempty" binding:"omitempty,min=1,max=255,alphanum" example:"updated-software"`
	Description          *string                `json:"description,omitempty" example:"An updated comprehensive software solution"`
	DistributionStrategy *string                `json:"distribution_strategy,omitempty" binding:"omitempty,oneof=licensed open" example:"licensed"`
	URL                  *string                `json:"url,omitempty" binding:"omitempty,url" example:"https://example.com/updated-product"`
	Platforms            []string               `json:"platforms,omitempty" example:"windows,macos,linux,android"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs

// ProductResponse represents a product in API responses
// @Description Product information returned by the API
type ProductResponse struct {
	ID                   string                 `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name                 string                 `json:"name" example:"My Software Product"`
	Code                 string                 `json:"code" example:"my-software"`
	Description          string                 `json:"description,omitempty" example:"A comprehensive software solution"`
	DistributionStrategy string                 `json:"distribution_strategy,omitempty" example:"licensed"`
	URL                  string                 `json:"url,omitempty" example:"https://example.com/product"`
	Platforms            []string               `json:"platforms,omitempty" example:"windows,macos,linux"`
	EnvironmentID        *string                `json:"environment_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440001"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
	Created              string                 `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated              string                 `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// ProductListResponse represents a paginated list of products
// @Description Paginated list of products
type ProductListResponse struct {
	Products   []ProductResponse `json:"products"`
	Pagination PaginationInfo    `json:"pagination"`
}

// Helper function to convert entity to response
func (h *ProductHandler) toProductResponse(product *entities.Product) ProductResponse {
	response := ProductResponse{
		ID:          product.ID,
		Name:        product.Name,
		Code:        product.Code,
		Description: product.Description,
		URL:         product.URL,
		Metadata:    product.Metadata,
		Created:     product.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:     product.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle pointer fields
	if product.DistributionStrategy != nil {
		response.DistributionStrategy = *product.DistributionStrategy
	}
	if product.EnvironmentID != nil {
		response.EnvironmentID = product.EnvironmentID
	}

	// Convert ProductPlatforms to []string
	if len(product.Platforms.Supported) > 0 {
		response.Platforms = product.Platforms.Supported
	}

	return response
}

// ListProducts handles GET /api/v1/products - Go-style approach
// @Summary List products
// @Description Get a paginated list of products for the authenticated account
// @Tags Products
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param search query string false "Search term for product name or code" example("software")
// @Param distribution_strategy query string false "Filter by distribution strategy" example("licensed")
// @Param environment_id query string false "Filter by environment ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} ProductListResponse "List of products retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /products [get]
func (h *ProductHandler) ListProducts(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	distributionStrategy := c.Query("distribution_strategy")
	environmentID := c.Query("environment_id")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if distributionStrategy != "" {
		filter.Filters["distribution_strategy"] = distributionStrategy
	}
	if environmentID != "" {
		filter.Filters["environment_id"] = environmentID
	}

	// Get products from repository
	products, total, err := h.serviceCoordinator.Repositories.Product().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve products",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	productResponses := make([]ProductResponse, len(products))
	for i, product := range products {
		productResponses[i] = h.toProductResponse(product)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := ProductListResponse{
		Products: productResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListProductsHandler - Legacy handler for backward compatibility
// Use ListProducts instead
func (h *ProductHandler) ListProductsHandler(c *gin.Context) {
	h.ListProducts(c)
}

// GetProduct handles GET /api/v1/products/:id - Go-style approach
// @Summary Get product by ID
// @Description Retrieve detailed information about a specific product
// @Tags Products
// @Accept json
// @Produce json
// @Param id path string true "Product ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} ProductResponse "Product retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid product ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Product not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /products/{id} [get]
func (h *ProductHandler) GetProduct(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid product ID format",
		})
		return
	}

	// Get product from repository by ID
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Product not found",
		})
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Product not found",
		})
		return
	}

	// Return product in Go-style
	response := h.toProductResponse(product)
	c.JSON(http.StatusOK, response)
}

// GetProductHandler - Legacy handler for backward compatibility
// Use GetProduct instead
func (h *ProductHandler) GetProductHandler(c *gin.Context) {
	h.GetProduct(c)
}

// CreateProduct handles POST /api/v1/products - Go-style approach
// @Summary Create a new product
// @Description Create a new product for license management
// @Tags Products
// @Accept json
// @Produce json
// @Param request body ProductCreateRequest true "Product creation request"
// @Success 201 {object} ProductResponse "Product created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /products [post]
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req ProductCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create product entity
	product := &entities.Product{
		AccountID:   accountID.String(),
		Name:        req.Name,
		Code:        req.Code,
		Key:         req.Code + "-" + uuid.New().String()[:8], // Generate unique key
		Description: req.Description,
		URL:         req.URL,
		Metadata:    req.Metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set optional fields
	if req.DistributionStrategy != "" {
		product.DistributionStrategy = &req.DistributionStrategy
	}
	if req.EnvironmentID != nil {
		product.EnvironmentID = req.EnvironmentID
	}

	// Set platforms
	if len(req.Platforms) > 0 {
		product.Platforms = entities.ProductPlatforms{
			Supported: req.Platforms,
		}
	}

	// Save product to repository
	if err := h.serviceCoordinator.Repositories.Product().Create(c.Request.Context(), product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create product",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductCreated,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	// Return created product in Go-style
	response := h.toProductResponse(product)
	c.Header("Location", "/api/v1/products/"+product.ID)
	c.JSON(http.StatusCreated, response)
}

// CreateProductHandler - Legacy handler for backward compatibility
// Use CreateProduct instead
func (h *ProductHandler) CreateProductHandler(c *gin.Context) {
	h.CreateProduct(c)
}

// UpdateProduct handles PUT /api/v1/products/:id - Go-style approach
// @Summary Update product
// @Description Update an existing product's properties
// @Tags Products
// @Accept json
// @Produce json
// @Param id path string true "Product ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body ProductUpdateRequest true "Product update request"
// @Success 200 {object} ProductResponse "Product updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or product ID"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Product not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /products/{id} [put]
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid product ID format",
		})
		return
	}

	var req ProductUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing product
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Product not found",
		})
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Product not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		product.Name = *req.Name
	}
	if req.Code != nil {
		product.Code = *req.Code
	}
	if req.Description != nil {
		product.Description = *req.Description
	}
	if req.DistributionStrategy != nil {
		product.DistributionStrategy = req.DistributionStrategy
	}
	if req.URL != nil {
		product.URL = *req.URL
	}
	if len(req.Platforms) > 0 {
		product.Platforms = entities.ProductPlatforms{
			Supported: req.Platforms,
		}
	}
	if req.Metadata != nil {
		product.Metadata = req.Metadata
	}
	product.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Product().Update(c.Request.Context(), product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update product",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductUpdated,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	// Return updated product in Go-style
	response := h.toProductResponse(product)
	c.JSON(http.StatusOK, response)
}

// UpdateProductHandler - Legacy handler for backward compatibility
// Use UpdateProduct instead
func (h *ProductHandler) UpdateProductHandler(c *gin.Context) {
	h.UpdateProduct(c)
}

// DeleteProductHandler deletes a product
// @Summary Delete product
// @Description Delete a product by ID (soft delete)
// @Tags Products
// @Accept json
// @Produce json
// @Param id path string true "Product ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "Product deleted successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid product ID format"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Product not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /products/{id} [delete]
func (h *ProductHandler) DeleteProductHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid product ID format")
		return
	}

	// Get product from repository
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Delete product
	if err := h.serviceCoordinator.Repositories.Product().Delete(c.Request.Context(), productID); err != nil {
		responses.RenderInternalError(c, "Failed to delete product: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductDeleted,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
