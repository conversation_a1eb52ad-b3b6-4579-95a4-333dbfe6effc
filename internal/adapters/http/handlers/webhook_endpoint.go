package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type WebhookEndpointHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewWebhookEndpointHandler(serviceCoordinator *services.ServiceCoordinator) *WebhookEndpointHandler {
	return &WebhookEndpointHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// WebhookEndpointRequest represents the request to create or update a webhook endpoint
// @Description Request payload for webhook endpoint operations
type WebhookEndpointRequest struct {
	Data struct {
		Type       string `json:"type" binding:"required" example:"webhook-endpoints"`
		Attributes struct {
			Name               *string  `json:"name,omitempty" example:"Production Webhook"`
			URL                string   `json:"url" binding:"required" example:"https://api.example.com/webhooks"`
			Events             []string `json:"events,omitempty" example:"license.created,license.updated"`
			SignatureAlgorithm *string  `json:"signature_algorithm,omitempty" example:"sha256"`
			APIVersion         *string  `json:"api_version,omitempty" example:"v1"`
			Enabled            *bool    `json:"enabled,omitempty" example:"true"`
			MaxRetries         *int     `json:"max_retries,omitempty" example:"3"`
			RetryDelay         *int     `json:"retry_delay,omitempty" example:"5"`
			Description        *string  `json:"description,omitempty" example:"Webhook for production environment"`
		} `json:"attributes"`
		Relationships *struct {
			Environment *struct {
				Data *struct {
					Type string `json:"type" example:"environments"`
					ID   string `json:"id" example:"550e8400-e29b-41d4-a716-************"`
				} `json:"data"`
			} `json:"environment,omitempty"`
		} `json:"relationships,omitempty"`
	} `json:"data"`
}

// ListWebhookEndpointsHandler lists webhook endpoints
// @Summary List webhook endpoints
// @Description Get a paginated list of webhook endpoints for the authenticated account
// @Tags Webhook Endpoints
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param limit query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param search query string false "Search term for webhook endpoint name or URL" example("production")
// @Success 200 {object} map[string]interface{} "List of webhook endpoints retrieved successfully"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /webhook-endpoints [get]
func (h *WebhookEndpointHandler) ListWebhookEndpointsHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Get webhook endpoints from repository
	webhookEndpoints, total, err := h.serviceCoordinator.Repositories.WebhookEndpoint().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve webhook endpoints: "+err.Error())
		return
	}

	response := gin.H{
		"data": webhookEndpoints,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetWebhookEndpointHandler gets a webhook endpoint by ID
// @Summary Get webhook endpoint by ID
// @Description Retrieve detailed information about a specific webhook endpoint
// @Tags Webhook Endpoints
// @Accept json
// @Produce json
// @Param id path string true "Webhook Endpoint ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} map[string]interface{} "Webhook endpoint retrieved successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid webhook endpoint ID format"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Webhook endpoint not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /webhook-endpoints/{id} [get]
func (h *WebhookEndpointHandler) GetWebhookEndpointHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	webhookEndpointIDStr := c.Param("id")
	webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
		return
	}

	// Get webhook endpoint from repository by ID
	webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
	if err != nil {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	// Verify webhook endpoint belongs to the account
	if webhookEndpoint.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	response := gin.H{
		"data": webhookEndpoint,
	}

	c.JSON(http.StatusOK, response)
}

// CreateWebhookEndpointHandler creates a webhook endpoint
// @Summary Create a new webhook endpoint
// @Description Create a new webhook endpoint for receiving event notifications
// @Tags Webhook Endpoints
// @Accept json
// @Produce json
// @Param request body WebhookEndpointRequest true "Webhook endpoint creation request"
// @Success 201 {object} map[string]interface{} "Webhook endpoint created successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid request data"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /webhook-endpoints [post]
func (h *WebhookEndpointHandler) CreateWebhookEndpointHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	var req WebhookEndpointRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Generate webhook secret
	secretBytes := make([]byte, 32)
	rand.Read(secretBytes)
	secret := hex.EncodeToString(secretBytes)

	// Create webhook endpoint through service layer
	webhookEndpoint := &entities.WebhookEndpoint{
		AccountID:     accountID.String(),
		URL:           req.Data.Attributes.URL,
		Events:        req.Data.Attributes.Events,
		Secret:        secret,
		SigningSecret: secret,
		Enabled:       true,
		MaxRetries:    3,
		RetryDelay:    5,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Set optional fields
	if req.Data.Attributes.Name != nil {
		webhookEndpoint.Name = *req.Data.Attributes.Name
	}
	if req.Data.Attributes.Description != nil {
		webhookEndpoint.Description = *req.Data.Attributes.Description
	}
	if req.Data.Attributes.Enabled != nil {
		webhookEndpoint.Enabled = *req.Data.Attributes.Enabled
	}
	if req.Data.Attributes.MaxRetries != nil {
		webhookEndpoint.MaxRetries = *req.Data.Attributes.MaxRetries
	}
	if req.Data.Attributes.RetryDelay != nil {
		webhookEndpoint.RetryDelay = *req.Data.Attributes.RetryDelay
	}

	// Set environment ID if provided
	if req.Data.Relationships != nil && req.Data.Relationships.Environment != nil {
		webhookEndpoint.EnvironmentID = &req.Data.Relationships.Environment.Data.ID
	}

	// Save webhook endpoint to repository
	if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Create(c.Request.Context(), webhookEndpoint); err != nil {
		responses.RenderInternalError(c, "Failed to create webhook endpoint: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventWebhookEndpointCreated,
		account,
		events.MakeEventResource(webhookEndpoint),
		events.EventMeta{},
	)

	response := gin.H{
		"data": webhookEndpoint,
	}

	c.Header("Location", "/api/v1/webhook-endpoints/"+webhookEndpoint.ID)
	c.JSON(http.StatusCreated, response)
}

// UpdateWebhookEndpointHandler updates a webhook endpoint
// @Summary Update webhook endpoint
// @Description Update an existing webhook endpoint's properties
// @Tags Webhook Endpoints
// @Accept json
// @Produce json
// @Param id path string true "Webhook Endpoint ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body WebhookEndpointRequest true "Webhook endpoint update request"
// @Success 200 {object} map[string]interface{} "Webhook endpoint updated successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid request data or webhook endpoint ID"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Webhook endpoint not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /webhook-endpoints/{id} [put]
func (h *WebhookEndpointHandler) UpdateWebhookEndpointHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	webhookEndpointIDStr := c.Param("id")
	webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
		return
	}

	var req WebhookEndpointRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Get existing webhook endpoint
	webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
	if err != nil {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	// Verify webhook endpoint belongs to the account
	if webhookEndpoint.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	// Update webhook endpoint fields
	webhookEndpoint.URL = req.Data.Attributes.URL
	if req.Data.Attributes.Name != nil {
		webhookEndpoint.Name = *req.Data.Attributes.Name
	}
	if req.Data.Attributes.Description != nil {
		webhookEndpoint.Description = *req.Data.Attributes.Description
	}
	if req.Data.Attributes.Events != nil {
		webhookEndpoint.Events = req.Data.Attributes.Events
	}
	if req.Data.Attributes.Enabled != nil {
		webhookEndpoint.Enabled = *req.Data.Attributes.Enabled
	}
	if req.Data.Attributes.MaxRetries != nil {
		webhookEndpoint.MaxRetries = *req.Data.Attributes.MaxRetries
	}
	if req.Data.Attributes.RetryDelay != nil {
		webhookEndpoint.RetryDelay = *req.Data.Attributes.RetryDelay
	}
	webhookEndpoint.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Update(c.Request.Context(), webhookEndpoint); err != nil {
		responses.RenderInternalError(c, "Failed to update webhook endpoint: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventWebhookEndpointUpdated,
		account,
		events.MakeEventResource(webhookEndpoint),
		events.EventMeta{},
	)

	response := gin.H{
		"data": webhookEndpoint,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteWebhookEndpointHandler deletes a webhook endpoint
// @Summary Delete webhook endpoint
// @Description Delete a webhook endpoint by ID (soft delete)
// @Tags Webhook Endpoints
// @Accept json
// @Produce json
// @Param id path string true "Webhook Endpoint ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "Webhook endpoint deleted successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid webhook endpoint ID format"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Webhook endpoint not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /webhook-endpoints/{id} [delete]
func (h *WebhookEndpointHandler) DeleteWebhookEndpointHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	webhookEndpointIDStr := c.Param("id")
	webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
		return
	}

	// Get webhook endpoint from repository
	webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
	if err != nil {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	// Verify webhook endpoint belongs to the account
	if webhookEndpoint.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Webhook endpoint not found")
		return
	}

	// Delete webhook endpoint
	if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Delete(c.Request.Context(), webhookEndpointID); err != nil {
		responses.RenderInternalError(c, "Failed to delete webhook endpoint: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventWebhookEndpointDeleted,
		account,
		events.MakeEventResource(webhookEndpoint),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
