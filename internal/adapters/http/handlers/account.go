package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
)

type AccountHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewAccountHandler(serviceCoordinator *services.ServiceCoordinator) *AccountHandler {
	return &AccountHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear

// AccountCreateRequest represents the request to create a new account
// @Description Request payload for creating a new account
type AccountCreateRequest struct {
	Name     string                 `json:"name" binding:"required,min=1,max=255" example:"Acme Corporation"`
	Slug     string                 `json:"slug" binding:"required,min=1,max=255,alphanum" example:"acme-corp"`
	Email    string                 `json:"email" binding:"required,email" example:"<EMAIL>"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// AccountUpdateRequest represents the request to update an existing account
// @Description Request payload for updating an account
type AccountUpdateRequest struct {
	Name     *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Updated Acme Corporation"`
	Slug     *string                `json:"slug,omitempty" binding:"omitempty,min=1,max=255,alphanum" example:"updated-acme"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs

// AccountResponse represents an account in API responses
// @Description Account information returned by the API
type AccountResponse struct {
	ID       string                 `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name     string                 `json:"name" example:"Acme Corporation"`
	Slug     string                 `json:"slug" example:"acme-corp"`
	Email    string                 `json:"email" example:"<EMAIL>"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	Created  string                 `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated  string                 `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// AccountListResponse represents a paginated list of accounts
// @Description Paginated list of accounts
type AccountListResponse struct {
	Accounts   []AccountResponse `json:"accounts"`
	Pagination PaginationInfo    `json:"pagination"`
}

// PaginationInfo represents pagination metadata
// @Description Pagination information for list responses
type PaginationInfo struct {
	Page       int   `json:"page" example:"1"`
	PerPage    int   `json:"per_page" example:"20"`
	Total      int64 `json:"total" example:"150"`
	TotalPages int   `json:"total_pages" example:"8"`
}

// Helper function to convert entity to response
func (h *AccountHandler) toAccountResponse(account *entities.Account) AccountResponse {
	return AccountResponse{
		ID:       account.ID,
		Name:     account.Name,
		Slug:     account.Slug,
		Email:    account.Email,
		Metadata: account.Metadata,
		Created:  account.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:  account.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}
}

// CreateAccount creates a new account
// @Summary Create a new account
// @Description Create a new account for license management
// @Tags Accounts
// @Accept json
// @Produce json
// @Param request body AccountCreateRequest true "Account creation request"
// @Success 201 {object} AccountResponse "Account created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /accounts [post]
func (h *AccountHandler) CreateAccount(c *gin.Context) {
	var req AccountCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create account entity
	account := &entities.Account{
		Name:     req.Name,
		Slug:     req.Slug,
		Email:    req.Email,
		Metadata: req.Metadata,
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Account().Create(c.Request.Context(), account); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create account",
			"details": err.Error(),
		})
		return
	}

	// Return created account
	response := h.toAccountResponse(account)
	c.JSON(http.StatusCreated, response)
}

// GetAccount retrieves a specific account by ID
// @Summary Get account by ID
// @Description Retrieve detailed information about a specific account
// @Tags Accounts
// @Accept json
// @Produce json
// @Param id path string true "Account ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} AccountResponse "Account retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid account ID format"
// @Failure 404 {object} map[string]interface{} "Account not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /accounts/{id} [get]
func (h *AccountHandler) GetAccount(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_parameter",
			"message": "Account ID is required",
		})
		return
	}

	// Parse UUID
	id, err := uuid.Parse(accountID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid account ID format",
		})
		return
	}

	// Get account from repository
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Account not found",
		})
		return
	}

	// Return account
	response := h.toAccountResponse(account)
	c.JSON(http.StatusOK, response)
}

// UpdateAccount updates an existing account
// @Summary Update account
// @Description Update an existing account's properties
// @Tags Accounts
// @Accept json
// @Produce json
// @Param id path string true "Account ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body AccountUpdateRequest true "Account update request"
// @Success 200 {object} AccountResponse "Account updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or account ID"
// @Failure 404 {object} map[string]interface{} "Account not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /accounts/{id} [put]
func (h *AccountHandler) UpdateAccount(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_parameter",
			"message": "Account ID is required",
		})
		return
	}

	// Parse UUID
	id, err := uuid.Parse(accountID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid account ID format",
		})
		return
	}

	var req AccountUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing account
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Account not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		account.Name = *req.Name
	}
	if req.Slug != nil {
		account.Slug = *req.Slug
	}
	if req.Metadata != nil {
		account.Metadata = req.Metadata
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Account().Update(c.Request.Context(), account); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update account",
			"details": err.Error(),
		})
		return
	}

	// Return updated account
	response := h.toAccountResponse(account)
	c.JSON(http.StatusOK, response)
}

// ListAccounts retrieves a paginated list of accounts
// @Summary List accounts
// @Description Get a paginated list of all accounts (admin only)
// @Tags Accounts
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Success 200 {object} AccountListResponse "List of accounts retrieved successfully"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /accounts [get]
func (h *AccountHandler) ListAccounts(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters (for future implementation)
	_ = c.Query("name")   // TODO: implement name filter
	_ = c.Query("slug")   // TODO: implement slug filter
	_ = c.Query("status") // TODO: implement status filter

	// Get accounts from repository with pagination and filters
	// TODO: Implement repository method with filters and pagination
	accounts := []entities.Account{} // Placeholder - would come from repository
	total := int64(0)                // Placeholder - would come from repository count

	// Convert to response format
	accountResponses := make([]AccountResponse, len(accounts))
	for i, account := range accounts {
		accountResponses[i] = h.toAccountResponse(&account)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := AccountListResponse{
		Accounts: accountResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// DeleteAccount handles DELETE /api/v1/accounts/:id
// @Summary Delete account
// @Description Delete an account by ID (soft delete)
// @Tags Accounts
// @Accept json
// @Produce json
// @Param id path string true "Account ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} map[string]interface{} "Account deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid account ID format"
// @Failure 404 {object} map[string]interface{} "Account not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /accounts/{id} [delete]
func (h *AccountHandler) DeleteAccount(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_parameter",
			"message": "Account ID is required",
		})
		return
	}

	// Parse UUID
	id, err := uuid.Parse(accountID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid account ID format",
		})
		return
	}

	// Check if account exists
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Account not found",
		})
		return
	}

	// Delete account
	if err := h.serviceCoordinator.Repositories.Account().Delete(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "deletion_failed",
			"message": "Failed to delete account",
			"details": err.Error(),
		})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{
		"message": "Account deleted successfully",
		"id":      account.ID,
	})
}

// GetCurrentAccount handles GET /api/v1/account (current account)
// @Summary Get current account
// @Description Retrieve information about the currently authenticated account
// @Tags Accounts
// @Accept json
// @Produce json
// @Success 200 {object} AccountResponse "Current account retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Account not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /account [get]
func (h *AccountHandler) GetCurrentAccount(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account not found",
		})
		return
	}

	// Get account from repository
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Account not found",
		})
		return
	}

	// Return current account
	response := h.toAccountResponse(account)
	c.JSON(http.StatusOK, response)
}

// UpdateCurrentAccount handles PUT /api/v1/account (current account)
// @Summary Update current account
// @Description Update the currently authenticated account's properties
// @Tags Accounts
// @Accept json
// @Produce json
// @Param request body AccountUpdateRequest true "Account update request"
// @Success 200 {object} AccountResponse "Current account updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Account not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /account [put]
func (h *AccountHandler) UpdateCurrentAccount(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account not found",
		})
		return
	}

	var req AccountUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get current account from repository
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Account not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		account.Name = *req.Name
	}
	if req.Slug != nil {
		account.Slug = *req.Slug
	}
	if req.Metadata != nil {
		account.Metadata = req.Metadata
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Account().Update(c.Request.Context(), account); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update account",
			"details": err.Error(),
		})
		return
	}

	// Return updated account
	response := h.toAccountResponse(account)
	c.JSON(http.StatusOK, response)
}

// ListAccountsHandler handles GET /api/v1/admin/accounts (admin only)
func (h *AccountHandler) ListAccountsHandler(c *gin.Context) {
	// Parse pagination
	page := 1
	limit := 25
	// Parse pagination parameters

	// Get accounts from repository with filters
	accounts := []entities.Account{} // Placeholder

	// Convert to resource objects
	resources := make([]interface{}, len(accounts))
	for i, account := range accounts {
		resources[i] = responses.ConvertAccount(&account)
	}

	// Create pagination metadata
	totalAccounts := int64(len(accounts))
	pagination := &responses.PaginationMeta{
		Page:       page,
		PerPage:    limit,
		Total:      totalAccounts,
		TotalPages: (totalAccounts + int64(limit) - 1) / int64(limit),
	}

	responses.RenderCollection(c, http.StatusOK, resources, pagination, nil)
}

// GetAccountDetailsHandler handles GET /api/v1/admin/accounts/:id (admin only)
func (h *AccountHandler) GetAccountDetailsHandler(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		responses.RenderBadRequest(c, "Account ID is required")
		return
	}

	// Get account from repository by ID
	account := &entities.Account{} // Placeholder

	resource := responses.ConvertAccount(account)
	responses.RenderSingle(c, http.StatusOK, resource, nil)
}
