package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type EntitlementHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewEntitlementHandler(serviceCoordinator *services.ServiceCoordinator) *EntitlementHandler {
	return &EntitlementHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear

// EntitlementCreateRequest represents the request to create a new entitlement
// @Description Request payload for creating a new entitlement
type EntitlementCreateRequest struct {
	Name          string                 `json:"name" binding:"required,min=1,max=255" example:"Premium Features"`
	Code          string                 `json:"code" binding:"required,min=1,max=255,alphanum" example:"premium-features"`
	EnvironmentID *string                `json:"environment_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// EntitlementUpdateRequest represents the request to update an existing entitlement
// @Description Request payload for updating an entitlement
type EntitlementUpdateRequest struct {
	Name     *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Updated Premium Features"`
	Code     *string                `json:"code,omitempty" binding:"omitempty,min=1,max=255,alphanum" example:"updated-premium"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs

// EntitlementResponse represents an entitlement in API responses
// @Description Entitlement information returned by the API
type EntitlementResponse struct {
	ID            string                 `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name          string                 `json:"name" example:"Premium Features"`
	Code          string                 `json:"code" example:"premium-features"`
	EnvironmentID *string                `json:"environment_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440001"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	Created       string                 `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated       string                 `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// EntitlementListResponse represents a paginated list of entitlements
// @Description Paginated list of entitlements
type EntitlementListResponse struct {
	Entitlements []EntitlementResponse `json:"entitlements"`
	Pagination   PaginationInfo        `json:"pagination"`
}

// Helper function to convert entity to response
func (h *EntitlementHandler) toEntitlementResponse(entitlement *entities.Entitlement) EntitlementResponse {
	return EntitlementResponse{
		ID:            entitlement.ID,
		Name:          entitlement.Name,
		Code:          entitlement.Code,
		EnvironmentID: entitlement.EnvironmentID,
		Metadata:      entitlement.Metadata,
		Created:       entitlement.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:       entitlement.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}
}

// ListEntitlements handles GET /api/v1/entitlements - Go-style approach
// @Summary List entitlements
// @Description Get a paginated list of entitlements for the authenticated account
// @Tags Entitlements
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param search query string false "Search term for entitlement name or code" example("premium")
// @Param environment_id query string false "Filter by environment ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} EntitlementListResponse "List of entitlements retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /entitlements [get]
func (h *EntitlementHandler) ListEntitlements(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	environmentID := c.Query("environment_id")

	// Build filter for repository
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if environmentID != "" {
		if eid, err := uuid.Parse(environmentID); err == nil {
			filter.Filters["environment_id"] = eid
		}
	}

	entitlements, total, err := h.serviceCoordinator.Repositories.Entitlement().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve entitlements",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	entitlementResponses := make([]EntitlementResponse, len(entitlements))
	for i, entitlement := range entitlements {
		entitlementResponses[i] = h.toEntitlementResponse(entitlement)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := EntitlementListResponse{
		Entitlements: entitlementResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListEntitlementsHandler - Legacy handler for backward compatibility
// Use ListEntitlements instead
func (h *EntitlementHandler) ListEntitlementsHandler(c *gin.Context) {
	h.ListEntitlements(c)
}

// GetEntitlement handles GET /api/v1/entitlements/:id - Go-style approach
// @Summary Get entitlement by ID
// @Description Retrieve detailed information about a specific entitlement
// @Tags Entitlements
// @Accept json
// @Produce json
// @Param id path string true "Entitlement ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} EntitlementResponse "Entitlement retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid entitlement ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Entitlement not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /entitlements/{id} [get]
func (h *EntitlementHandler) GetEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	entitlementIDStr := c.Param("id")
	entitlementID, err := uuid.Parse(entitlementIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid entitlement ID format",
		})
		return
	}

	// Get entitlement from repository by ID
	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Verify entitlement belongs to the account
	if entitlement.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Return entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.JSON(http.StatusOK, response)
}

// GetEntitlementHandler - Legacy handler for backward compatibility
// Use GetEntitlement instead
func (h *EntitlementHandler) GetEntitlementHandler(c *gin.Context) {
	h.GetEntitlement(c)
}

// CreateEntitlement handles POST /api/v1/entitlements - Go-style approach
// @Summary Create a new entitlement
// @Description Create a new entitlement for feature access control
// @Tags Entitlements
// @Accept json
// @Produce json
// @Param request body EntitlementCreateRequest true "Entitlement creation request"
// @Success 201 {object} EntitlementResponse "Entitlement created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /entitlements [post]
func (h *EntitlementHandler) CreateEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req EntitlementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create entitlement entity
	entitlement := &entities.Entitlement{
		AccountID:     accountID.String(),
		Name:          req.Name,
		Code:          req.Code,
		EnvironmentID: req.EnvironmentID,
		Metadata:      req.Metadata,
	}

	// Save entitlement to repository
	if err := h.serviceCoordinator.Repositories.Entitlement().Create(c.Request.Context(), entitlement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create entitlement",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementCreated,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	// Return created entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.Header("Location", "/api/v1/entitlements/"+entitlement.ID)
	c.JSON(http.StatusCreated, response)
}

// CreateEntitlementHandler - Legacy handler for backward compatibility
// Use CreateEntitlement instead
func (h *EntitlementHandler) CreateEntitlementHandler(c *gin.Context) {
	h.CreateEntitlement(c)
}

// UpdateEntitlement handles PUT /api/v1/entitlements/:id - Go-style approach
// @Summary Update entitlement
// @Description Update an existing entitlement's properties
// @Tags Entitlements
// @Accept json
// @Produce json
// @Param id path string true "Entitlement ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body EntitlementUpdateRequest true "Entitlement update request"
// @Success 200 {object} EntitlementResponse "Entitlement updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or entitlement ID"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Entitlement not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /entitlements/{id} [put]
func (h *EntitlementHandler) UpdateEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	entitlementIDStr := c.Param("id")
	entitlementID, err := uuid.Parse(entitlementIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid entitlement ID format",
		})
		return
	}

	var req EntitlementUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing entitlement
	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Verify entitlement belongs to the account
	if entitlement.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		entitlement.Name = *req.Name
	}
	if req.Code != nil {
		entitlement.Code = *req.Code
	}
	if req.Metadata != nil {
		entitlement.Metadata = req.Metadata
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Entitlement().Update(c.Request.Context(), entitlement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update entitlement",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementUpdated,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	// Return updated entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.JSON(http.StatusOK, response)
}

// UpdateEntitlementHandler - Legacy handler for backward compatibility
// Use UpdateEntitlement instead
func (h *EntitlementHandler) UpdateEntitlementHandler(c *gin.Context) {
	h.UpdateEntitlement(c)
}

// DeleteEntitlementHandler deletes an entitlement
// @Summary Delete entitlement
// @Description Delete an entitlement by ID (soft delete)
// @Tags Entitlements
// @Accept json
// @Produce json
// @Param id path string true "Entitlement ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "Entitlement deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid entitlement ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Entitlement not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /entitlements/{id} [delete]
func (h *EntitlementHandler) DeleteEntitlementHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
		return
	}

	entitlementID := c.Param("id")
	if entitlementID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entitlement ID is required"})
		return
	}

	entitlementUUID, err := uuid.Parse(entitlementID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entitlement ID format"})
		return
	}

	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Entitlement not found"})
		return
	}

	if err := h.serviceCoordinator.Repositories.Entitlement().Delete(c.Request.Context(), entitlementUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete entitlement"})
		return
	}

	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementDeleted,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
