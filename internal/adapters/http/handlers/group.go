package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type GroupHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewGroupHandler(serviceCoordinator *services.ServiceCoordinator) *GroupHandler {
	return &GroupHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear

// GroupCreateRequest represents the request to create a new group
// @Description Request payload for creating a new group
type GroupCreateRequest struct {
	Name          string                 `json:"name" binding:"required,min=1,max=255" example:"Development Team"`
	MaxUsers      *int                   `json:"max_users,omitempty" binding:"omitempty,min=0" example:"10"`
	MaxLicenses   *int                   `json:"max_licenses,omitempty" binding:"omitempty,min=0" example:"5"`
	MaxMachines   *int                   `json:"max_machines,omitempty" binding:"omitempty,min=0" example:"20"`
	EnvironmentID *string                `json:"environment_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// GroupUpdateRequest represents the request to update an existing group
// @Description Request payload for updating a group
type GroupUpdateRequest struct {
	Name        *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Updated Development Team"`
	MaxUsers    *int                   `json:"max_users,omitempty" binding:"omitempty,min=0" example:"15"`
	MaxLicenses *int                   `json:"max_licenses,omitempty" binding:"omitempty,min=0" example:"8"`
	MaxMachines *int                   `json:"max_machines,omitempty" binding:"omitempty,min=0" example:"30"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs

// GroupResponse represents a group in API responses
// @Description Group information returned by the API
type GroupResponse struct {
	ID            string                 `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name          string                 `json:"name" example:"Development Team"`
	MaxUsers      *int                   `json:"max_users,omitempty" example:"10"`
	MaxLicenses   *int                   `json:"max_licenses,omitempty" example:"5"`
	MaxMachines   *int                   `json:"max_machines,omitempty" example:"20"`
	EnvironmentID *string                `json:"environment_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440001"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	Created       string                 `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated       string                 `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// GroupListResponse represents a paginated list of groups
// @Description Paginated list of groups
type GroupListResponse struct {
	Groups     []GroupResponse `json:"groups"`
	Pagination PaginationInfo  `json:"pagination"`
}

// Helper function to convert entity to response
func (h *GroupHandler) toGroupResponse(group *entities.Group) GroupResponse {
	return GroupResponse{
		ID:            group.ID,
		Name:          group.Name,
		MaxUsers:      group.MaxUsers,
		MaxLicenses:   group.MaxLicenses,
		MaxMachines:   group.MaxMachines,
		EnvironmentID: group.EnvironmentID,
		Metadata:      group.Metadata,
		Created:       group.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:       group.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}
}

// ListGroups handles GET /api/v1/groups - Go-style approach
// @Summary List groups
// @Description Get a paginated list of groups for the authenticated account
// @Tags Groups
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param search query string false "Search term for group name" example("development")
// @Param environment_id query string false "Filter by environment ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} GroupListResponse "List of groups retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /groups [get]
func (h *GroupHandler) ListGroups(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	environmentID := c.Query("environment_id")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if environmentID != "" {
		if eid, err := uuid.Parse(environmentID); err == nil {
			filter.Filters["environment_id"] = eid
		}
	}

	// Get groups from repository
	groups, total, err := h.serviceCoordinator.Repositories.Group().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve groups",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	groupResponses := make([]GroupResponse, len(groups))
	for i, group := range groups {
		groupResponses[i] = h.toGroupResponse(group)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := GroupListResponse{
		Groups: groupResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListGroupsHandler - Legacy handler for backward compatibility
// Use ListGroups instead
func (h *GroupHandler) ListGroupsHandler(c *gin.Context) {
	h.ListGroups(c)
}

// GetGroup handles GET /api/v1/groups/:id - Go-style approach
// @Summary Get group by ID
// @Description Retrieve detailed information about a specific group
// @Tags Groups
// @Accept json
// @Produce json
// @Param id path string true "Group ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} GroupResponse "Group retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid group ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Group not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /groups/{id} [get]
func (h *GroupHandler) GetGroup(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid group ID format",
		})
		return
	}

	// Get group from repository by ID
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Group not found",
		})
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Group not found",
		})
		return
	}

	// Return group in Go-style
	response := h.toGroupResponse(group)
	c.JSON(http.StatusOK, response)
}

// GetGroupHandler - Legacy handler for backward compatibility
// Use GetGroup instead
func (h *GroupHandler) GetGroupHandler(c *gin.Context) {
	h.GetGroup(c)
}

// CreateGroup handles POST /api/v1/groups - Go-style approach
// @Summary Create a new group
// @Description Create a new group for organizing users and resources
// @Tags Groups
// @Accept json
// @Produce json
// @Param request body GroupCreateRequest true "Group creation request"
// @Success 201 {object} GroupResponse "Group created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /groups [post]
func (h *GroupHandler) CreateGroup(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req GroupCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create group entity
	group := &entities.Group{
		AccountID:     accountID.String(),
		Name:          req.Name,
		MaxUsers:      req.MaxUsers,
		MaxLicenses:   req.MaxLicenses,
		MaxMachines:   req.MaxMachines,
		EnvironmentID: req.EnvironmentID,
		Metadata:      req.Metadata,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Save group to repository
	if err := h.serviceCoordinator.Repositories.Group().Create(c.Request.Context(), group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create group",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupCreated,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	// Return created group in Go-style
	response := h.toGroupResponse(group)
	c.Header("Location", "/api/v1/groups/"+group.ID)
	c.JSON(http.StatusCreated, response)
}

// CreateGroupHandler - Legacy handler for backward compatibility
// Use CreateGroup instead
func (h *GroupHandler) CreateGroupHandler(c *gin.Context) {
	h.CreateGroup(c)
}

// UpdateGroup handles PUT /api/v1/groups/:id - Go-style approach
// @Summary Update group
// @Description Update an existing group's properties
// @Tags Groups
// @Accept json
// @Produce json
// @Param id path string true "Group ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body GroupUpdateRequest true "Group update request"
// @Success 200 {object} GroupResponse "Group updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or group ID"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Group not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /groups/{id} [put]
func (h *GroupHandler) UpdateGroup(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid group ID format",
		})
		return
	}

	var req GroupUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing group
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Group not found",
		})
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Group not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		group.Name = *req.Name
	}
	if req.MaxUsers != nil {
		group.MaxUsers = req.MaxUsers
	}
	if req.MaxLicenses != nil {
		group.MaxLicenses = req.MaxLicenses
	}
	if req.MaxMachines != nil {
		group.MaxMachines = req.MaxMachines
	}
	if req.Metadata != nil {
		group.Metadata = req.Metadata
	}
	group.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Group().Update(c.Request.Context(), group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update group",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupUpdated,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	// Return updated group in Go-style
	response := h.toGroupResponse(group)
	c.JSON(http.StatusOK, response)
}

// UpdateGroupHandler - Legacy handler for backward compatibility
// Use UpdateGroup instead
func (h *GroupHandler) UpdateGroupHandler(c *gin.Context) {
	h.UpdateGroup(c)
}

// DeleteGroupHandler deletes a group
// @Summary Delete group
// @Description Delete a group by ID (soft delete)
// @Tags Groups
// @Accept json
// @Produce json
// @Param id path string true "Group ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "Group deleted successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid group ID format"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Group not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /groups/{id} [delete]
func (h *GroupHandler) DeleteGroupHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid group ID format")
		return
	}

	// Get group from repository
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Delete group
	if err := h.serviceCoordinator.Repositories.Group().Delete(c.Request.Context(), groupID); err != nil {
		responses.RenderInternalError(c, "Failed to delete group: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupDeleted,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
