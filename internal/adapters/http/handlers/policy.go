package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type PolicyHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewPolicyHandler(serviceCoordinator *services.ServiceCoordinator) *PolicyHandler {
	return &PolicyHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear

// PolicyCreateRequest represents the request to create a new policy
// @Description Request payload for creating a new policy
type PolicyCreateRequest struct {
	Name             string  `json:"name" binding:"required,min=1,max=255" example:"Standard License Policy"`
	Description      string  `json:"description,omitempty" example:"A standard policy for regular licenses"`
	ProductID        string  `json:"product_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	EnvironmentID    *string `json:"environment_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0" example:"365"`
	Strict           bool    `json:"strict" example:"true"`
	Floating         bool    `json:"floating" example:"false"`
	RequireHeartbeat bool    `json:"require_heartbeat" example:"true"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0" example:"5"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0" example:"10"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0" example:"3"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0" example:"8"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0" example:"100"`

	// Advanced settings (simplified)
	Scheme                    string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN" example:"ED25519_SIGN"`
	HeartbeatDuration         *int   `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0" example:"3600"`
	MachineUniquenessStrategy string `json:"machine_uniqueness_strategy,omitempty" binding:"omitempty,oneof=UNIQUE_PER_ACCOUNT UNIQUE_PER_PRODUCT UNIQUE_PER_POLICY UNIQUE_PER_LICENSE" example:"UNIQUE_PER_LICENSE"`
	ExpirationStrategy        string `json:"expiration_strategy,omitempty" binding:"omitempty,oneof=RESTRICT_ACCESS REVOKE_ACCESS MAINTAIN_ACCESS" example:"RESTRICT_ACCESS"`
	OverageStrategy           string `json:"overage_strategy,omitempty" binding:"omitempty,oneof=NO_OVERAGE ALWAYS_ALLOW_OVERAGE ALLOW_1_25X_OVERAGE ALLOW_1_5X_OVERAGE ALLOW_2X_OVERAGE" example:"NO_OVERAGE"`

	// Check-in settings
	RequireCheckIn       bool    `json:"require_check_in" example:"false"`
	CheckInInterval      *string `json:"check_in_interval,omitempty" example:"daily"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0" example:"1"`

	// Pool and activation limits
	UsePool          bool `json:"use_pool" example:"false"`
	MaxActivations   *int `json:"max_activations,omitempty" binding:"omitempty,min=0" example:"10"`
	MaxDeactivations *int `json:"max_deactivations,omitempty" binding:"omitempty,min=0" example:"5"`

	Protected bool                   `json:"protected" example:"false"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// PolicyUpdateRequest represents the request to update an existing policy
// @Description Request payload for updating a policy
type PolicyUpdateRequest struct {
	Name             *string `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Updated Standard License Policy"`
	Description      *string `json:"description,omitempty" example:"An updated standard policy for regular licenses"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0" example:"730"`
	Strict           *bool   `json:"strict,omitempty" example:"false"`
	Floating         *bool   `json:"floating,omitempty" example:"true"`
	RequireHeartbeat *bool   `json:"require_heartbeat,omitempty" example:"false"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0" example:"10"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0" example:"20"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0" example:"5"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0" example:"16"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0" example:"200"`

	// Advanced settings
	Scheme                    *string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN" example:"RSA_PKCS1_SIGN"`
	HeartbeatDuration         *int    `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0" example:"7200"`
	MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy,omitempty" example:"UNIQUE_PER_POLICY"`
	ExpirationStrategy        *string `json:"expiration_strategy,omitempty" example:"REVOKE_ACCESS"`
	OverageStrategy           *string `json:"overage_strategy,omitempty" example:"ALLOW_1_25X_OVERAGE"`

	// Check-in settings
	RequireCheckIn       *bool   `json:"require_check_in,omitempty" example:"true"`
	CheckInInterval      *string `json:"check_in_interval,omitempty" example:"weekly"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0" example:"2"`

	// Pool and activation limits
	UsePool          *bool `json:"use_pool,omitempty" example:"true"`
	MaxActivations   *int  `json:"max_activations,omitempty" binding:"omitempty,min=0" example:"20"`
	MaxDeactivations *int  `json:"max_deactivations,omitempty" binding:"omitempty,min=0" example:"10"`

	Protected *bool                  `json:"protected,omitempty" example:"true"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs

// PolicyResponse represents a policy in API responses
// @Description Policy information returned by the API
type PolicyResponse struct {
	ID                        string                 `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name                      string                 `json:"name" example:"Standard License Policy"`
	Description               string                 `json:"description,omitempty" example:"A standard policy for regular licenses"`
	ProductID                 string                 `json:"product_id" example:"550e8400-e29b-41d4-a716-************"`
	EnvironmentID             *string                `json:"environment_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440002"`
	Duration                  *int                   `json:"duration,omitempty" example:"365"`
	Strict                    bool                   `json:"strict" example:"true"`
	Floating                  bool                   `json:"floating" example:"false"`
	RequireHeartbeat          bool                   `json:"require_heartbeat" example:"true"`
	MaxMachines               *int                   `json:"max_machines,omitempty" example:"5"`
	MaxProcesses              *int                   `json:"max_processes,omitempty" example:"10"`
	MaxUsers                  *int                   `json:"max_users,omitempty" example:"3"`
	MaxCores                  *int                   `json:"max_cores,omitempty" example:"8"`
	MaxUses                   *int                   `json:"max_uses,omitempty" example:"100"`
	Scheme                    string                 `json:"scheme,omitempty" example:"ED25519_SIGN"`
	HeartbeatDuration         *int                   `json:"heartbeat_duration,omitempty" example:"3600"`
	MachineUniquenessStrategy string                 `json:"machine_uniqueness_strategy,omitempty" example:"UNIQUE_PER_LICENSE"`
	ExpirationStrategy        string                 `json:"expiration_strategy,omitempty" example:"RESTRICT_ACCESS"`
	OverageStrategy           string                 `json:"overage_strategy,omitempty" example:"NO_OVERAGE"`
	RequireCheckIn            bool                   `json:"require_check_in" example:"false"`
	CheckInInterval           *string                `json:"check_in_interval,omitempty" example:"daily"`
	CheckInIntervalCount      *int                   `json:"check_in_interval_count,omitempty" example:"1"`
	UsePool                   bool                   `json:"use_pool" example:"false"`
	MaxActivations            *int                   `json:"max_activations,omitempty" example:"10"`
	MaxDeactivations          *int                   `json:"max_deactivations,omitempty" example:"5"`
	Protected                 bool                   `json:"protected" example:"false"`
	Metadata                  map[string]interface{} `json:"metadata,omitempty"`
	Created                   string                 `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated                   string                 `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// PolicyListResponse represents a paginated list of policies
// @Description Paginated list of policies
type PolicyListResponse struct {
	Policies   []PolicyResponse `json:"policies"`
	Pagination PaginationInfo   `json:"pagination"`
}

// Helper function to convert entity to response
func (h *PolicyHandler) toPolicyResponse(policy *entities.Policy) PolicyResponse {
	response := PolicyResponse{
		ID:                   policy.ID,
		Name:                 policy.Name,
		Description:          policy.Description,
		ProductID:            policy.ProductID,
		EnvironmentID:        policy.EnvironmentID,
		Duration:             policy.Duration,
		Strict:               policy.Strict,
		Floating:             policy.Floating,
		RequireHeartbeat:     policy.RequireHeartbeat,
		MaxMachines:          policy.MaxMachines,
		MaxProcesses:         policy.MaxProcesses,
		MaxUsers:             policy.MaxUsers,
		MaxCores:             policy.MaxCores,
		MaxUses:              policy.MaxUses,
		HeartbeatDuration:    policy.HeartbeatDuration,
		RequireCheckIn:       policy.RequireCheckIn,
		CheckInInterval:      policy.CheckInInterval,
		CheckInIntervalCount: policy.CheckInIntervalCount,
		UsePool:              policy.UsePool,
		MaxActivations:       policy.MaxActivations,
		MaxDeactivations:     policy.MaxDeactivations,
		Metadata:             policy.Metadata,
		Created:              policy.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:              policy.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle pointer fields
	if policy.Protected != nil {
		response.Protected = *policy.Protected
	}

	// Handle enum fields
	if policy.Scheme != "" {
		response.Scheme = string(policy.Scheme)
	}
	if policy.MachineUniquenessStrategy != nil && *policy.MachineUniquenessStrategy != "" {
		response.MachineUniquenessStrategy = *policy.MachineUniquenessStrategy
	}
	if policy.ExpirationStrategy != nil && *policy.ExpirationStrategy != "" {
		response.ExpirationStrategy = *policy.ExpirationStrategy
	}
	if policy.OverageStrategy != nil && *policy.OverageStrategy != "" {
		response.OverageStrategy = *policy.OverageStrategy
	}

	return response
}

// ListPolicies handles GET /api/v1/policies - Go-style approach
// @Summary List policies
// @Description Get a paginated list of policies for the authenticated account
// @Tags Policies
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param search query string false "Search term for policy name" example("standard")
// @Param product_id query string false "Filter by product ID" example("550e8400-e29b-41d4-a716-************")
// @Param environment_id query string false "Filter by environment ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} PolicyListResponse "List of policies retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /policies [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	productID := c.Query("product_id")
	environmentID := c.Query("environment_id")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if productID != "" {
		if pid, err := uuid.Parse(productID); err == nil {
			filter.Filters["product_id"] = pid
		}
	}
	if environmentID != "" {
		if eid, err := uuid.Parse(environmentID); err == nil {
			filter.Filters["environment_id"] = eid
		}
	}

	// Get policies from repository with filters
	policies, total, err := h.serviceCoordinator.Repositories.Policy().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve policies",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	policyResponses := make([]PolicyResponse, len(policies))
	for i, policy := range policies {
		policyResponses[i] = h.toPolicyResponse(policy)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := PolicyListResponse{
		Policies: policyResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListPoliciesHandler - Legacy handler for backward compatibility
// Use ListPolicies instead
func (h *PolicyHandler) ListPoliciesHandler(c *gin.Context) {
	h.ListPolicies(c)
}

// GetPolicy handles GET /api/v1/policies/:id - Go-style approach
// @Summary Get policy by ID
// @Description Retrieve detailed information about a specific policy
// @Tags Policies
// @Accept json
// @Produce json
// @Param id path string true "Policy ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} PolicyResponse "Policy retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid policy ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Policy not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /policies/{id} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid policy ID format",
		})
		return
	}

	// Get policy from repository by ID
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Return policy in Go-style
	response := h.toPolicyResponse(policy)
	c.JSON(http.StatusOK, response)
}

// GetPolicyHandler - Legacy handler for backward compatibility
// Use GetPolicy instead
func (h *PolicyHandler) GetPolicyHandler(c *gin.Context) {
	h.GetPolicy(c)
}

// CreatePolicy handles POST /api/v1/policies - Go-style approach
// @Summary Create a new policy
// @Description Create a new policy for license management
// @Tags Policies
// @Accept json
// @Produce json
// @Param request body PolicyCreateRequest true "Policy creation request"
// @Success 201 {object} PolicyResponse "Policy created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /policies [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req PolicyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create policy entity
	policy := &entities.Policy{
		AccountID:            accountID.String(),
		ProductID:            req.ProductID,
		Name:                 req.Name,
		Description:          req.Description,
		Duration:             req.Duration,
		Strict:               req.Strict,
		Floating:             req.Floating,
		RequireHeartbeat:     req.RequireHeartbeat,
		MaxMachines:          req.MaxMachines,
		MaxProcesses:         req.MaxProcesses,
		MaxUsers:             req.MaxUsers,
		MaxCores:             req.MaxCores,
		MaxUses:              req.MaxUses,
		HeartbeatDuration:    req.HeartbeatDuration,
		RequireCheckIn:       req.RequireCheckIn,
		CheckInInterval:      req.CheckInInterval,
		CheckInIntervalCount: req.CheckInIntervalCount,
		UsePool:              req.UsePool,
		MaxActivations:       req.MaxActivations,
		MaxDeactivations:     req.MaxDeactivations,
		Protected:            &req.Protected,
		Metadata:             req.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Set optional fields
	if req.EnvironmentID != nil {
		policy.EnvironmentID = req.EnvironmentID
	}

	// Set enum fields
	if req.Scheme != "" {
		policy.Scheme = entities.LicenseScheme(req.Scheme)
	} else {
		policy.Scheme = entities.Ed25519Sign // Default
	}

	if req.MachineUniquenessStrategy != "" {
		policy.MachineUniquenessStrategy = &req.MachineUniquenessStrategy
	}
	if req.ExpirationStrategy != "" {
		policy.ExpirationStrategy = &req.ExpirationStrategy
	}
	if req.OverageStrategy != "" {
		policy.OverageStrategy = &req.OverageStrategy
	}

	// Save policy to repository
	if err := h.serviceCoordinator.Repositories.Policy().Create(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create policy",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyCreated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	// Return created policy in Go-style
	response := h.toPolicyResponse(policy)
	c.Header("Location", "/api/v1/policies/"+policy.ID)
	c.JSON(http.StatusCreated, response)
}

// CreatePolicyHandler - Legacy handler for backward compatibility
// Use CreatePolicy instead
func (h *PolicyHandler) CreatePolicyHandler(c *gin.Context) {
	h.CreatePolicy(c)
}

// UpdatePolicy handles PUT /api/v1/policies/:id - Go-style approach
// @Summary Update policy
// @Description Update an existing policy's properties
// @Tags Policies
// @Accept json
// @Produce json
// @Param id path string true "Policy ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body PolicyUpdateRequest true "Policy update request"
// @Success 200 {object} PolicyResponse "Policy updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or policy ID"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Policy not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /policies/{id} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid policy ID format",
		})
		return
	}

	var req PolicyUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing policy
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		policy.Name = *req.Name
	}
	if req.Description != nil {
		policy.Description = *req.Description
	}
	if req.Duration != nil {
		policy.Duration = req.Duration
	}
	if req.Strict != nil {
		policy.Strict = *req.Strict
	}
	if req.Floating != nil {
		policy.Floating = *req.Floating
	}
	if req.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *req.RequireHeartbeat
	}
	if req.HeartbeatDuration != nil {
		policy.HeartbeatDuration = req.HeartbeatDuration
	}
	if req.RequireCheckIn != nil {
		policy.RequireCheckIn = *req.RequireCheckIn
	}
	if req.CheckInInterval != nil {
		policy.CheckInInterval = req.CheckInInterval
	}
	if req.CheckInIntervalCount != nil {
		policy.CheckInIntervalCount = req.CheckInIntervalCount
	}
	if req.UsePool != nil {
		policy.UsePool = *req.UsePool
	}
	if req.MaxMachines != nil {
		policy.MaxMachines = req.MaxMachines
	}
	if req.MaxProcesses != nil {
		policy.MaxProcesses = req.MaxProcesses
	}
	if req.MaxUsers != nil {
		policy.MaxUsers = req.MaxUsers
	}
	if req.MaxCores != nil {
		policy.MaxCores = req.MaxCores
	}
	if req.MaxUses != nil {
		policy.MaxUses = req.MaxUses
	}
	if req.MaxActivations != nil {
		policy.MaxActivations = req.MaxActivations
	}
	if req.MaxDeactivations != nil {
		policy.MaxDeactivations = req.MaxDeactivations
	}
	if req.Protected != nil {
		policy.Protected = req.Protected
	}
	if req.Metadata != nil {
		policy.Metadata = req.Metadata
	}

	// Update enum fields
	if req.Scheme != nil && *req.Scheme != "" {
		policy.Scheme = entities.LicenseScheme(*req.Scheme)
	}
	if req.MachineUniquenessStrategy != nil && *req.MachineUniquenessStrategy != "" {
		policy.MachineUniquenessStrategy = req.MachineUniquenessStrategy
	}
	if req.ExpirationStrategy != nil && *req.ExpirationStrategy != "" {
		policy.ExpirationStrategy = req.ExpirationStrategy
	}
	if req.OverageStrategy != nil && *req.OverageStrategy != "" {
		policy.OverageStrategy = req.OverageStrategy
	}
	policy.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Policy().Update(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update policy",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyUpdated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	// Return updated policy in Go-style
	response := h.toPolicyResponse(policy)
	c.JSON(http.StatusOK, response)
}

// UpdatePolicyHandler - Legacy handler for backward compatibility
// Use UpdatePolicy instead
func (h *PolicyHandler) UpdatePolicyHandler(c *gin.Context) {
	h.UpdatePolicy(c)
}

// DeletePolicyHandler deletes a policy
// @Summary Delete policy
// @Description Delete a policy by ID (soft delete)
// @Tags Policies
// @Accept json
// @Produce json
// @Param id path string true "Policy ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "Policy deleted successfully"
// @Failure 400 {object} responses.ErrorResponse "Invalid policy ID format"
// @Failure 401 {object} responses.ErrorResponse "Authentication required"
// @Failure 404 {object} responses.ErrorResponse "Policy not found"
// @Failure 500 {object} responses.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /policies/{id} [delete]
func (h *PolicyHandler) DeletePolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid policy ID format")
		return
	}

	// Get policy from repository
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Delete policy
	if err := h.serviceCoordinator.Repositories.Policy().Delete(c.Request.Context(), policyID); err != nil {
		responses.RenderInternalError(c, "Failed to delete policy: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyDeleted,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
