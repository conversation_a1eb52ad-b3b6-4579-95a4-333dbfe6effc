package middleware

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
)

// AuthContext keys for storing authentication information
type AuthContextKey string

const (
	UserIDKey      AuthContextKey = "user_id"
	AccountIDKey   AuthContextKey = "account_id"
	TokenClaimsKey AuthContextKey = "token_claims"
	PermissionsKey AuthContextKey = "permissions"
)

// AuthConfig holds configuration for authentication middleware
type AuthConfig struct {
	PublicKey        string
	TokenHeader      string
	TokenPrefix      string
	SecretKey        string // For HMAC token verification
	SkipVerification bool   // For development/testing
}

// AuthMiddleware provides JWT-based authentication
type AuthMiddleware struct {
	serviceCoordinator *services.ServiceCoordinator
	config             AuthConfig
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(serviceCoordinator *services.ServiceCoordinator, config AuthConfig) *AuthMiddleware {
	if config.TokenHeader == "" {
		config.TokenHeader = "Authorization"
	}
	if config.TokenPrefix == "" {
		config.TokenPrefix = "Bearer "
	}

	return &AuthMiddleware{
		serviceCoordinator: serviceCoordinator,
		config:             config,
	}
}

// RequireAuth middleware that requires valid JWT authentication
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from header
		token, err := am.extractToken(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Valid authentication token required",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Skip verification if configured (for development)
		if am.config.SkipVerification {
			c.Set(string(UserIDKey), "dev-user")
			c.Set(string(AccountIDKey), "dev-account")
			c.Next()
			return
		}

		// Validate token
		_, allClaims, err := am.serviceCoordinator.Crypto.JWT.VerifyToken(token, am.config.PublicKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_token",
				"message": "Authentication token is invalid or expired",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Extract user and account information
		userID, accountID, err := am.extractUserInfo(allClaims)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_token_claims",
				"message": "Token does not contain required user information",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Store authentication information in context
		c.Set(string(UserIDKey), userID)
		c.Set(string(AccountIDKey), accountID)
		c.Set(string(TokenClaimsKey), allClaims)

		// Extract permissions if available
		if permissions, ok := allClaims["permissions"].([]interface{}); ok {
			permissionStrings := make([]string, len(permissions))
			for i, perm := range permissions {
				if permStr, ok := perm.(string); ok {
					permissionStrings[i] = permStr
				}
			}
			c.Set(string(PermissionsKey), permissionStrings)
		}

		c.Next()
	}
}

// OptionalAuth middleware that allows both authenticated and unauthenticated requests
func (am *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to extract token
		token, err := am.extractToken(c)
		if err != nil {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		// Skip verification if configured
		if am.config.SkipVerification {
			c.Set(string(UserIDKey), "dev-user")
			c.Set(string(AccountIDKey), "dev-account")
			c.Next()
			return
		}

		// Try to validate token
		_, allClaims, err := am.serviceCoordinator.Crypto.JWT.VerifyToken(token, am.config.PublicKey)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Extract user information if token is valid
		userID, accountID, err := am.extractUserInfo(allClaims)
		if err == nil {
			c.Set(string(UserIDKey), userID)
			c.Set(string(AccountIDKey), accountID)
			c.Set(string(TokenClaimsKey), allClaims)

			// Extract permissions if available
			if permissions, ok := allClaims["permissions"].([]interface{}); ok {
				permissionStrings := make([]string, len(permissions))
				for i, perm := range permissions {
					if permStr, ok := perm.(string); ok {
						permissionStrings[i] = permStr
					}
				}
				c.Set(string(PermissionsKey), permissionStrings)
			}
		}

		c.Next()
	}
}

// APIKeyAuth middleware for API key-based authentication
func (am *AuthMiddleware) APIKeyAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try header first
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			// Try query parameter
			apiKey = c.Query("api_key")
		}

		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "api_key_required",
				"message": "API key required in X-API-Key header or api_key parameter",
			})
			c.Abort()
			return
		}

		// Look up API key in database
		ctx := context.Background()
		// Use GetByDigest instead of GetByBearer
		tempToken := &entities.Token{}
		tempToken.Token = apiKey
		if err := tempToken.GenerateToken(am.config.SecretKey); err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_api_key",
				"message": "Failed to process API key",
			})
			c.Abort()
			return
		}

		token, err := am.serviceCoordinator.Repositories.Token().GetByDigest(ctx, tempToken.Digest)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_api_key",
				"message": "API key is invalid or not found",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "api_key_expired",
				"message": "API key has expired",
			})
			c.Abort()
			return
		}

		// Set authentication context
		bearerID, _ := token.GetBearerUUID()
		accountID, _ := token.GetAccountUUID()
		c.Set(string(UserIDKey), bearerID.String())
		c.Set(string(AccountIDKey), accountID.String())

		c.Next()
	}
}

// LicenseKeyAuth middleware for license key-based authentication (Ruby: http_license_authenticator)
func (am *AuthMiddleware) LicenseKeyAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to extract license key from various sources
		licenseKey, err := am.extractLicenseKey(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "license_key_required",
				"message": "License key required for authentication",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Get account ID from URL parameters (Ruby: current_account from scope)
		accountIDStr := c.Param("account_id")
		if accountIDStr == "" {
			// Try to get from query or context if not in URL
			accountIDStr = c.Query("account_id")
		}

		if accountIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "account_required",
				"message": "Account ID required for license authentication",
			})
			c.Abort()
			return
		}

		_, err = uuid.Parse(accountIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "invalid_account_id",
				"message": "Invalid account ID format",
			})
			c.Abort()
			return
		}

		// Use LicenseKeyLookupService to find and validate the license
		// Ruby: LicenseKeyLookupService.call(environment: current_environment, account: current_account, key: license_key)
		lookupService := am.serviceCoordinator.LicenseValidation // Access to license services

		// For now, use basic validation - would need proper lookup service
		result, err := lookupService.ValidateLicense(
			c.Request.Context(),
			licenseKey,
			nil, // machine fingerprint
			nil, // environment
		)
		if err != nil || !result.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "license_invalid",
				"message": "License key is invalid or not found",
			})
			c.Abort()
			return
		}

		// Ruby validation: current_license.account_id != current_account.id
		if result.License != nil && result.License.AccountID != accountIDStr {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "license_invalid",
				"message": "License does not belong to this account",
			})
			c.Abort()
			return
		}

		// Set authentication context (Ruby: @current_bearer = current_license)
		c.Set(string(UserIDKey), "license:"+licenseKey) // Special user ID for license auth
		c.Set(string(AccountIDKey), accountIDStr)
		c.Set("license_key", licenseKey)
		c.Set("auth_scheme", "license")

		// TODO: Set current license in context for authorization
		if result.License != nil {
			c.Set("current_license", result.License)
		}

		c.Next()
	}
}

// extractLicenseKey extracts license key from request (Ruby: multiple authentication methods)
func (am *AuthMiddleware) extractLicenseKey(c *gin.Context) (string, error) {
	// Ruby: has_bearer_credentials? - Check Bearer token
	if authHeader := c.GetHeader("Authorization"); authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer "), nil
		}
		// Ruby: has_basic_credentials? - Check Basic auth with license: prefix
		if strings.HasPrefix(authHeader, "Basic ") {
			// Decode basic auth
			credentials := strings.TrimPrefix(authHeader, "Basic ")
			decoded, err := base64.StdEncoding.DecodeString(credentials)
			if err == nil {
				parts := strings.SplitN(string(decoded), ":", 2)
				if len(parts) == 2 && parts[0] == "license" {
					return parts[1], nil
				}
			}
		}
	}

	// Ruby: query_token_authenticator - Check query parameters
	if queryToken := c.Query("token"); queryToken != "" {
		parts := strings.SplitN(queryToken, ":", 2)
		if len(parts) == 2 && parts[0] == "license" {
			return parts[1], nil
		}
		// Direct license key in query
		return queryToken, nil
	}

	// Ruby: has_license_credentials? - Check License header
	if licenseHeader := c.GetHeader("License"); licenseHeader != "" {
		return licenseHeader, nil
	}

	// Check license_key parameter
	if licenseKey := c.Query("license_key"); licenseKey != "" {
		return licenseKey, nil
	}

	return "", fmt.Errorf("no license key found in Authorization header, License header, or query parameters")
}

// MultiAuth middleware that supports multiple authentication methods (Ruby: authenticate_with_token)
func (am *AuthMiddleware) MultiAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try Bearer token first (JWT)
		if authHeader := c.GetHeader("Authorization"); authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			am.RequireAuth()(c)
			return
		}

		// Try Basic auth (could be license or token)
		if authHeader := c.GetHeader("Authorization"); authHeader != "" && strings.HasPrefix(authHeader, "Basic ") {
			// Try license auth first
			am.LicenseKeyAuth()(c)
			if !c.IsAborted() {
				return
			}
			c.Next() // Reset abort state
		}

		// Try License header
		if c.GetHeader("License") != "" {
			am.LicenseKeyAuth()(c)
			return
		}

		// Try API key
		if c.GetHeader("X-API-Key") != "" || c.Query("api_key") != "" {
			am.APIKeyAuth()(c)
			return
		}

		// Try query token (license or token)
		if c.Query("token") != "" {
			am.LicenseKeyAuth()(c)
			if !c.IsAborted() {
				return
			}
		}

		// No valid authentication found
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "authentication_required",
			"message": "Valid authentication required (Bearer token, License key, API key, or Basic auth)",
		})
		c.Abort()
	}
}

// extractToken extracts JWT token from request headers
func (am *AuthMiddleware) extractToken(c *gin.Context) (string, error) {
	authHeader := c.GetHeader(am.config.TokenHeader)
	if authHeader == "" {
		return "", fmt.Errorf("no %s header provided", am.config.TokenHeader)
	}

	if !strings.HasPrefix(authHeader, am.config.TokenPrefix) {
		return "", fmt.Errorf("invalid %s header format", am.config.TokenHeader)
	}

	token := strings.TrimPrefix(authHeader, am.config.TokenPrefix)
	if token == "" {
		return "", fmt.Errorf("empty token in %s header", am.config.TokenHeader)
	}

	return token, nil
}

// extractUserInfo extracts user ID and account ID from token claims
func (am *AuthMiddleware) extractUserInfo(claims map[string]interface{}) (string, string, error) {
	// Extract subject (user ID)
	userID, ok := claims["sub"].(string)
	if !ok || userID == "" {
		return "", "", fmt.Errorf("missing or invalid subject claim")
	}

	// Extract account ID
	accountID, ok := claims["account_id"].(string)
	if !ok || accountID == "" {
		return "", "", fmt.Errorf("missing or invalid account_id claim")
	}

	// Validate UUIDs
	if _, err := uuid.Parse(userID); err != nil {
		return "", "", fmt.Errorf("invalid user ID format: %w", err)
	}

	if _, err := uuid.Parse(accountID); err != nil {
		return "", "", fmt.Errorf("invalid account ID format: %w", err)
	}

	return userID, accountID, nil
}

// Helper functions for accessing authentication context

// GetUserID extracts user ID from Gin context
func GetUserID(c *gin.Context) (uuid.UUID, error) {
	userIDStr, exists := c.Get(string(UserIDKey))
	if !exists {
		return uuid.Nil, fmt.Errorf("user ID not found in context")
	}

	userIDString, ok := userIDStr.(string)
	if !ok {
		return uuid.Nil, fmt.Errorf("user ID has invalid type")
	}

	return uuid.Parse(userIDString)
}

// GetAccountID extracts account ID from Gin context
func GetAccountID(c *gin.Context) (uuid.UUID, error) {
	accountIDStr, exists := c.Get(string(AccountIDKey))
	if !exists {
		return uuid.Nil, fmt.Errorf("account ID not found in context")
	}

	accountIDString, ok := accountIDStr.(string)
	if !ok {
		return uuid.Nil, fmt.Errorf("account ID has invalid type")
	}

	return uuid.Parse(accountIDString)
}

// GetTokenClaims extracts all token claims from Gin context
func GetTokenClaims(c *gin.Context) (map[string]interface{}, error) {
	claims, exists := c.Get(string(TokenClaimsKey))
	if !exists {
		return nil, fmt.Errorf("token claims not found in context")
	}

	claimsMap, ok := claims.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("token claims have invalid type")
	}

	return claimsMap, nil
}

// GetPermissions extracts user permissions from Gin context
func GetPermissions(c *gin.Context) ([]string, error) {
	permissions, exists := c.Get(string(PermissionsKey))
	if !exists {
		return []string{}, nil // Return empty slice if no permissions
	}

	permissionsList, ok := permissions.([]string)
	if !ok {
		return nil, fmt.Errorf("permissions have invalid type")
	}

	return permissionsList, nil
}

// IsAuthenticated checks if the request is authenticated
func IsAuthenticated(c *gin.Context) bool {
	_, exists := c.Get(string(UserIDKey))
	return exists
}

// RequirePermission creates a middleware that requires specific permission
func (am *AuthMiddleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissions, err := GetPermissions(c)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "permission_error",
				"message": "Unable to check permissions",
			})
			c.Abort()
			return
		}

		// Check if user has the required permission
		hasPermission := false
		for _, perm := range permissions {
			if perm == permission || perm == "*" { // "*" is admin permission
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "insufficient_permissions",
				"message": fmt.Sprintf("Permission '%s' required", permission),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission creates a middleware that requires any of the specified permissions
func (am *AuthMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userPermissions, err := GetPermissions(c)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "permission_error",
				"message": "Unable to check permissions",
			})
			c.Abort()
			return
		}

		// Check if user has any of the required permissions
		hasPermission := false
		for _, userPerm := range userPermissions {
			if userPerm == "*" {
				hasPermission = true
				break
			}
			for _, requiredPerm := range permissions {
				if userPerm == requiredPerm {
					hasPermission = true
					break
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "insufficient_permissions",
				"message": fmt.Sprintf("One of the following permissions required: %v", permissions),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
