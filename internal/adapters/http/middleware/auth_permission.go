package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/auth"
)

// PermissionMiddleware handles permission-based authorization
type PermissionMiddleware struct{}

// NewPermissionMiddleware creates a new permission middleware
func NewPermissionMiddleware() *PermissionMiddleware {
	return &PermissionMiddleware{}
}

// RequirePermissions creates middleware that requires specific permissions
func (pm *PermissionMiddleware) RequirePermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if token has required permissions
		if !token.HasAllPermissions(permissions...) {
			c.J<PERSON>(http.StatusForbidden, gin.H{
				"error":                "insufficient_permissions",
				"message":              "Insufficient permissions to access this resource",
				"required_permissions": permissions,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission creates middleware that requires any of the specified permissions
func (pm *PermissionMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if token has any of the required permissions
		if !token.HasAnyPermission(permissions...) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":                "insufficient_permissions",
				"message":              "Insufficient permissions to access this resource",
				"required_permissions": permissions,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole creates middleware that requires a specific token type/role
func (pm *PermissionMiddleware) RequireRole(allowedRoles ...auth.TokenType) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if token type is allowed
		roleAllowed := false
		for _, allowedRole := range allowedRoles {
			if token.TokenType == allowedRole {
				roleAllowed = true
				break
			}
		}

		if !roleAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":         "role_not_allowed",
				"message":       "Token role is not allowed to access this resource",
				"token_type":    token.TokenType,
				"allowed_roles": allowedRoles,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdminRole creates middleware that requires admin or developer role
func (pm *PermissionMiddleware) RequireAdminRole() gin.HandlerFunc {
	return pm.RequireRole(auth.TokenTypeAdmin, auth.TokenTypeDeveloper)
}

// RequireUserRole creates middleware that requires user role or higher
func (pm *PermissionMiddleware) RequireUserRole() gin.HandlerFunc {
	return pm.RequireRole(
		auth.TokenTypeAdmin,
		auth.TokenTypeDeveloper,
		auth.TokenTypeUser,
		auth.TokenTypeSalesAgent,
		auth.TokenTypeSupportAgent,
	)
}

// RequireEnvironmentRole creates middleware that requires environment role or higher
func (pm *PermissionMiddleware) RequireEnvironmentRole() gin.HandlerFunc {
	return pm.RequireRole(
		auth.TokenTypeAdmin,
		auth.TokenTypeDeveloper,
		auth.TokenTypeEnvironment,
	)
}

// RequireProductRole creates middleware that requires product role or higher
func (pm *PermissionMiddleware) RequireProductRole() gin.HandlerFunc {
	return pm.RequireRole(
		auth.TokenTypeAdmin,
		auth.TokenTypeDeveloper,
		auth.TokenTypeEnvironment,
		auth.TokenTypeProduct,
	)
}

// RequireWildcardPermission creates middleware that requires wildcard permission
func (pm *PermissionMiddleware) RequireWildcardPermission() gin.HandlerFunc {
	return pm.RequirePermissions(auth.WildcardPermission)
}

// RequireLicensePermissions creates middleware for license-related operations
func (pm *PermissionMiddleware) RequireLicensePermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"license.read",
		"license.validate",
		"license.create",
		"license.update",
		"license.delete",
	)
}

// RequireLicenseValidatePermission creates middleware for license validation
func (pm *PermissionMiddleware) RequireLicenseValidatePermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"license.validate",
		"license.read",
	)
}

// RequireMachinePermissions creates middleware for machine-related operations
func (pm *PermissionMiddleware) RequireMachinePermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"machine.read",
		"machine.create",
		"machine.update",
		"machine.delete",
		"machine.heartbeat.ping",
		"machine.heartbeat.reset",
	)
}

// RequireMachineHeartbeatPermission creates middleware for machine heartbeat operations
func (pm *PermissionMiddleware) RequireMachineHeartbeatPermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"machine.heartbeat.ping",
		"machine.heartbeat.reset",
	)
}

// RequireAccountReadPermission creates middleware for account read operations
func (pm *PermissionMiddleware) RequireAccountReadPermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"account.read",
	)
}

// RequireTokenPermissions creates middleware for token-related operations
func (pm *PermissionMiddleware) RequireTokenPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"token.read",
		"token.generate",
		"token.regenerate",
		"token.revoke",
	)
}

// RequireUserPermissions creates middleware for user-related operations
func (pm *PermissionMiddleware) RequireUserPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"user.read",
		"user.create",
		"user.update",
		"user.delete",
	)
}

// RequireProductPermissions creates middleware for product-related operations
func (pm *PermissionMiddleware) RequireProductPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"product.read",
		"product.create",
		"product.update",
		"product.delete",
	)
}

// RequirePolicyPermissions creates middleware for policy-related operations
func (pm *PermissionMiddleware) RequirePolicyPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"policy.read",
		"policy.create",
		"policy.update",
		"policy.delete",
	)
}

// RequireGroupPermissions creates middleware for group-related operations
func (pm *PermissionMiddleware) RequireGroupPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"group.read",
		"group.create",
		"group.update",
		"group.delete",
	)
}

// RequireEntitlementPermissions creates middleware for entitlement-related operations
func (pm *PermissionMiddleware) RequireEntitlementPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"entitlement.read",
		"entitlement.create",
		"entitlement.update",
		"entitlement.delete",
	)
}
