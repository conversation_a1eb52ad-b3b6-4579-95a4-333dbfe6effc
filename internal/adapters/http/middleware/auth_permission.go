package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/auth"
	"github.com/gokeys/gokeys/internal/domain/entities"
)

// PermissionMiddleware handles permission-based authorization
type PermissionMiddleware struct{}

// NewPermissionMiddleware creates a new permission middleware
func NewPermissionMiddleware() *PermissionMiddleware {
	return &PermissionMiddleware{}
}

// RequirePermissions creates middleware that requires specific permissions
func (pm *PermissionMiddleware) RequirePermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if token has required permissions
		if !token.HasAllPermissions(permissions...) {
			c.J<PERSON>(http.StatusForbidden, gin.H{
				"error":                "insufficient_permissions",
				"message":              "Insufficient permissions to access this resource",
				"required_permissions": permissions,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission creates middleware that requires any of the specified permissions
func (pm *PermissionMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if token has any of the required permissions
		if !token.HasAnyPermission(permissions...) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":                "insufficient_permissions",
				"message":              "Insufficient permissions to access this resource",
				"required_permissions": permissions,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole creates middleware that requires a specific bearer type/role
func (pm *PermissionMiddleware) RequireRole(allowedRoles ...entities.TokenBearerType) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := GetToken(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required to access this resource",
			})
			c.Abort()
			return
		}

		// Check if token is expired
		if token.IsExpired() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "token_expired",
				"message": "Token has expired",
			})
			c.Abort()
			return
		}

		// Check if bearer type is allowed
		roleAllowed := false
		for _, allowedRole := range allowedRoles {
			if token.BearerType == allowedRole {
				roleAllowed = true
				break
			}
		}

		if !roleAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":         "role_not_allowed",
				"message":       "Token role is not allowed to access this resource",
				"bearer_type":   token.BearerType,
				"allowed_roles": allowedRoles,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdminRole creates middleware that requires admin role (user bearer type with admin permissions)
func (pm *PermissionMiddleware) RequireAdminRole() gin.HandlerFunc {
	return pm.RequirePermissions(auth.WildcardPermission)
}

// RequireUserRole creates middleware that requires user role or higher
func (pm *PermissionMiddleware) RequireUserRole() gin.HandlerFunc {
	return pm.RequireRole(entities.TokenBearerTypeUser)
}

// RequireEnvironmentRole creates middleware that requires environment role or higher
func (pm *PermissionMiddleware) RequireEnvironmentRole() gin.HandlerFunc {
	return pm.RequireRole(entities.TokenBearerTypeEnvironment)
}

// RequireProductRole creates middleware that requires product role or higher
func (pm *PermissionMiddleware) RequireProductRole() gin.HandlerFunc {
	return pm.RequireRole(entities.TokenBearerTypeProduct)
}

// RequireLicenseRole creates middleware that requires license role
func (pm *PermissionMiddleware) RequireLicenseRole() gin.HandlerFunc {
	return pm.RequireRole(entities.TokenBearerTypeLicense)
}

// RequireWildcardPermission creates middleware that requires wildcard permission
func (pm *PermissionMiddleware) RequireWildcardPermission() gin.HandlerFunc {
	return pm.RequirePermissions(auth.WildcardPermission)
}

// RequireLicensePermissions creates middleware for license-related operations
func (pm *PermissionMiddleware) RequireLicensePermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"license.read",
		"license.validate",
		"license.create",
		"license.update",
		"license.delete",
	)
}

// RequireLicenseValidatePermission creates middleware for license validation
func (pm *PermissionMiddleware) RequireLicenseValidatePermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"license.validate",
		"license.read",
	)
}

// RequireMachinePermissions creates middleware for machine-related operations
func (pm *PermissionMiddleware) RequireMachinePermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"machine.read",
		"machine.create",
		"machine.update",
		"machine.delete",
		"machine.heartbeat.ping",
		"machine.heartbeat.reset",
	)
}

// RequireMachineHeartbeatPermission creates middleware for machine heartbeat operations
func (pm *PermissionMiddleware) RequireMachineHeartbeatPermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"machine.heartbeat.ping",
		"machine.heartbeat.reset",
	)
}

// RequireAccountReadPermission creates middleware for account read operations
func (pm *PermissionMiddleware) RequireAccountReadPermission() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"account.read",
	)
}

// RequireTokenPermissions creates middleware for token-related operations
func (pm *PermissionMiddleware) RequireTokenPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"token.read",
		"token.generate",
		"token.regenerate",
		"token.revoke",
	)
}

// RequireUserPermissions creates middleware for user-related operations
func (pm *PermissionMiddleware) RequireUserPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"user.read",
		"user.create",
		"user.update",
		"user.delete",
	)
}

// RequireProductPermissions creates middleware for product-related operations
func (pm *PermissionMiddleware) RequireProductPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"product.read",
		"product.create",
		"product.update",
		"product.delete",
	)
}

// RequirePolicyPermissions creates middleware for policy-related operations
func (pm *PermissionMiddleware) RequirePolicyPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"policy.read",
		"policy.create",
		"policy.update",
		"policy.delete",
	)
}

// RequireGroupPermissions creates middleware for group-related operations
func (pm *PermissionMiddleware) RequireGroupPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"group.read",
		"group.create",
		"group.update",
		"group.delete",
	)
}

// RequireEntitlementPermissions creates middleware for entitlement-related operations
func (pm *PermissionMiddleware) RequireEntitlementPermissions() gin.HandlerFunc {
	return pm.RequireAnyPermission(
		auth.WildcardPermission,
		"entitlement.read",
		"entitlement.create",
		"entitlement.update",
		"entitlement.delete",
	)
}
