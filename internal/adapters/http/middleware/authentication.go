package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
)

// AuthenticationMiddleware handles token authentication
type AuthenticationMiddleware struct {
	serviceCoordinator *services.ServiceCoordinator
	secretKey          string
}

// NewAuthenticationMiddleware creates a new authentication middleware
func NewAuthenticationMiddleware(serviceCoordinator *services.ServiceCoordinator, secretKey string) *AuthenticationMiddleware {
	return &AuthenticationMiddleware{
		serviceCoordinator: serviceCoordinator,
		secretKey:          secretKey,
	}
}

// AuthenticationResult holds the result of authentication
type AuthenticationResult struct {
	Token       *entities.Token
	AccountID   uuid.UUID
	BearerID    uuid.UUID
	BearerType  string
	Permissions []string
}

// RequireAuthentication middleware that requires valid authentication
func (am *AuthenticationMiddleware) RequireAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		result, err := am.authenticateRequest(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": err.Error(),
			})
			c.Abort()
			return
		}

		// Store authentication result in context
		am.setAuthContext(c, result)
		c.Next()
	}
}

// OptionalAuthentication middleware that allows both authenticated and unauthenticated requests
func (am *AuthenticationMiddleware) OptionalAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		result, err := am.authenticateRequest(c)
		if err == nil && result != nil {
			// Store authentication result in context if successful
			am.setAuthContext(c, result)
		}
		// Continue regardless of authentication result
		c.Next()
	}
}

// authenticateRequest performs the actual authentication logic
func (am *AuthenticationMiddleware) authenticateRequest(c *gin.Context) (*AuthenticationResult, error) {
	// Try different authentication methods in order
	if result, err := am.tryBearerTokenAuth(c); err == nil && result != nil {
		return result, nil
	}

	if result, err := am.tryBasicAuth(c); err == nil && result != nil {
		return result, nil
	}

	if result, err := am.tryLicenseAuth(c); err == nil && result != nil {
		return result, nil
	}

	if result, err := am.tryQueryTokenAuth(c); err == nil && result != nil {
		return result, nil
	}

	return nil, fmt.Errorf("no valid authentication provided")
}

// tryBearerTokenAuth attempts Bearer token authentication
func (am *AuthenticationMiddleware) tryBearerTokenAuth(c *gin.Context) (*AuthenticationResult, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
		return nil, fmt.Errorf("no bearer token provided")
	}

	rawToken := strings.TrimPrefix(authHeader, "Bearer ")
	return am.authenticateToken(rawToken)
}

// tryBasicAuth attempts Basic authentication (for tokens, not passwords)
func (am *AuthenticationMiddleware) tryBasicAuth(c *gin.Context) (*AuthenticationResult, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || !strings.HasPrefix(authHeader, "Basic ") {
		return nil, fmt.Errorf("no basic auth provided")
	}

	// In Keygen, Basic auth is used for tokens and license keys, not passwords
	username, password, ok := c.Request.BasicAuth()
	if !ok {
		return nil, fmt.Errorf("invalid basic auth format")
	}

	// Try token authentication
	if username == "token" || username == "" {
		return am.authenticateToken(password)
	}

	// Try license key authentication
	if username == "license" {
		return am.authenticateLicenseKey(password)
	}

	return nil, fmt.Errorf("unsupported basic auth username")
}

// tryLicenseAuth attempts License key authentication via Authorization header
func (am *AuthenticationMiddleware) tryLicenseAuth(c *gin.Context) (*AuthenticationResult, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || !strings.HasPrefix(authHeader, "License ") {
		return nil, fmt.Errorf("no license auth provided")
	}

	licenseKey := strings.TrimPrefix(authHeader, "License ")
	return am.authenticateLicenseKey(licenseKey)
}

// tryQueryTokenAuth attempts query parameter token authentication
func (am *AuthenticationMiddleware) tryQueryTokenAuth(c *gin.Context) (*AuthenticationResult, error) {
	queryToken := c.Query("token")
	if queryToken == "" {
		queryToken = c.Query("auth")
	}
	if queryToken == "" {
		return nil, fmt.Errorf("no query token provided")
	}

	// Parse query token format: "type:token" or just "token"
	parts := strings.SplitN(queryToken, ":", 2)
	if len(parts) == 2 {
		tokenType := parts[0]
		token := parts[1]

		switch tokenType {
		case "license":
			return am.authenticateLicenseKey(token)
		case "token":
			return am.authenticateToken(token)
		default:
			return nil, fmt.Errorf("unsupported query token type: %s", tokenType)
		}
	}

	// Default to token authentication for backward compatibility
	return am.authenticateToken(queryToken)
}

// authenticateToken authenticates a token
func (am *AuthenticationMiddleware) authenticateToken(rawToken string) (*AuthenticationResult, error) {
	if rawToken == "" {
		return nil, fmt.Errorf("empty token")
	}

	// Create HMAC digest of the raw token
	tempToken := &entities.Token{}
	tempToken.Token = rawToken
	if err := tempToken.GenerateToken(am.secretKey); err != nil {
		return nil, fmt.Errorf("failed to generate token digest: %w", err)
	}

	// Look up token by digest
	ctx := context.Background()
	token, err := am.serviceCoordinator.Repositories.Token().GetByDigest(ctx, tempToken.Digest)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Verify the token
	if !token.VerifyToken(rawToken, am.secretKey) {
		return nil, fmt.Errorf("invalid token")
	}

	// Check if token is expired
	if token.IsExpired() {
		return nil, fmt.Errorf("token expired")
	}

	// Check if token is revoked
	if token.IsRevoked() {
		return nil, fmt.Errorf("token revoked")
	}

	// Parse UUIDs
	accountID, err := uuid.Parse(token.AccountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	bearerID, err := uuid.Parse(token.BearerID)
	if err != nil {
		return nil, fmt.Errorf("invalid bearer ID: %w", err)
	}

	// Create authentication result
	result := &AuthenticationResult{
		Token:       token,
		AccountID:   accountID,
		BearerID:    bearerID,
		BearerType:  string(token.BearerType),
		Permissions: token.CustomPermissions,
	}

	return result, nil
}

// authenticateLicenseKey authenticates a license key
func (am *AuthenticationMiddleware) authenticateLicenseKey(licenseKey string) (*AuthenticationResult, error) {
	if licenseKey == "" {
		return nil, fmt.Errorf("empty license key")
	}

	// TODO: Implement license key authentication
	// This would involve:
	// 1. Query licenses table by key
	// 2. Check if license is active
	// 3. Check if license supports token auth (policy setting)
	// 4. Create authentication result with license permissions

	// For now, return a placeholder implementation
	return nil, fmt.Errorf("license key authentication not yet implemented")
}

// setAuthContext stores authentication information in the Gin context
func (am *AuthenticationMiddleware) setAuthContext(c *gin.Context, result *AuthenticationResult) {
	c.Set("token", result.Token)
	c.Set("account_id", result.AccountID)
	c.Set("bearer_id", result.BearerID)
	c.Set("bearer_type", result.BearerType)
	c.Set("permissions", result.Permissions)
}

// GetToken retrieves the authenticated token from context
func GetToken(c *gin.Context) (*entities.Token, bool) {
	if token, exists := c.Get("token"); exists {
		if t, ok := token.(*entities.Token); ok {
			return t, true
		}
	}
	return nil, false
}

// GetBearerID retrieves the bearer ID from context
func GetBearerID(c *gin.Context) (uuid.UUID, error) {
	if bearerID, exists := c.Get("bearer_id"); exists {
		if id, ok := bearerID.(uuid.UUID); ok {
			return id, nil
		}
	}
	return uuid.Nil, fmt.Errorf("bearer ID not found in context")
}

// GetBearerType retrieves the bearer type from context
func GetBearerType(c *gin.Context) (string, error) {
	if bearerType, exists := c.Get("bearer_type"); exists {
		if bt, ok := bearerType.(string); ok {
			return bt, nil
		}
	}
	return "", fmt.Errorf("bearer type not found in context")
}
