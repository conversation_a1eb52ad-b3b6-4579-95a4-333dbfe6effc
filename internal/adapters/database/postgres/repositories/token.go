package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TokenRepositoryImpl implements TokenRepository interface
type TokenRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Token]
}

// NewTokenRepository creates a new token repository
func NewTokenRepository(db *gorm.DB) repositories.TokenRepository {
	return &TokenRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Token](db),
	}
}

// GetByDigest retrieves a token by its digest
func (r *TokenRepositoryImpl) GetByDigest(ctx context.Context, digest string) (*entities.Token, error) {
	var token entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("digest = ?", digest).
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByBearer retrieves tokens by bearer ID and type
func (r *TokenRepositoryImpl) GetByBearer(ctx context.Context, bearerID uuid.UUID, bearerType string) ([]*entities.Token, error) {
	var tokens []*entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("bearer_id = ? AND bearer_type = ?", bearerID, bearerType).
		Find(&tokens).Error
	return tokens, err
}

// DeleteExpired removes expired tokens
func (r *TokenRepositoryImpl) DeleteExpired(ctx context.Context) error {
	result := r.GetDB().WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&entities.Token{})
	return result.Error
}

// GetByAccount retrieves all tokens for an account
func (r *TokenRepositoryImpl) GetByAccount(ctx context.Context, accountID uuid.UUID) ([]*entities.Token, error) {
	var tokens []*entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("account_id = ?", accountID).
		Find(&tokens).Error
	return tokens, err
}

// UpdateLastUsed updates the token's last used timestamp
func (r *TokenRepositoryImpl) UpdateLastUsed(ctx context.Context, tokenID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.Token{}).
		Where("id = ?", tokenID).
		Updates(map[string]interface{}{
			"last_used":  time.Now(),
			"updated_at": time.Now(),
		}).Error
}
