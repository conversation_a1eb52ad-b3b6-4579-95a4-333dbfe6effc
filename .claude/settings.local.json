{"permissions": {"allow": ["<PERSON><PERSON>(go test:*)", "Bash(find:*)", "<PERSON><PERSON>(createdb:*)", "<PERSON><PERSON>(dropdb:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(grep:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(npx swagger2openapi:*)", "<PERSON><PERSON>(curl:*)", "Bash(go install:*)", "<PERSON>sh(swag init:*)", "Bash(~/go/bin/swag init:*)", "<PERSON><PERSON>(go:*)", "Bash(~/go/bin/swag:*)", "Bash(rm:*)"], "deny": []}}